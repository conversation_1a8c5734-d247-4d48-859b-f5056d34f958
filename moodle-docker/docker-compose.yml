# All sensitive/environment-specific values are loaded from a .env file at the project root.
services:
  php:
    build: ./php
    env_file:
      - .env
    environment:
      - MOODLE_DB_HOST=${MOODLE_DB_HOST}
      - M<PERSON><PERSON><PERSON>_DB_NAME=${MO<PERSON><PERSON>_DB_NAME}
      - M<PERSON><PERSON>LE_DB_USER=${MOODLE_DB_USER}
      - MOODLE_DB_PASS=${MOODLE_DB_PASS}
      - MOODLE_DB_PREFIX=${MOODLE_DB_PREFIX}
      - MOODLE_DB_PORT=${MOODLE_DB_PORT}
      - MOODLE_WWWROOT=${MOODLE_WWWROOT}
      - MOODLE_DATAROOT=${MOODLE_DATAROOT}
      - MOODLE_ADMIN=${MOODLE_ADMIN}
      - MO<PERSON>LE_SESSION_TIMEOUT=${MOODLE_SESSION_TIMEOUT}
      - MOODLE_SESSION_WARNING=${MO<PERSON><PERSON>_SESSION_WARNING}
      - M<PERSON><PERSON><PERSON>_SESSION_COOKIE=${M<PERSON><PERSON><PERSON>_SESSION_COOKIE}
    container_name: moodle-php
    healthcheck:
      test: ["CMD-SHELL", "php -v || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    volumes:
      - ./php/custom-configs/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./php/custom-configs/php-fpm.conf:/usr/local/etc/php-fpm.d/php-fpm.conf
      - ./php/moodledata:/var/moodledata
      - ./moodle:/var/www/html
    networks:
      - moodle-net
    depends_on:
      - mysql

  nginx:
    image: nginx:latest
    container_name: moodle-nginx
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/nginx-health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/certs:/etc/nginx/certs:ro
      - ./moodle:/var/www/html:ro
      - ./php/moodledata:/var/moodledata:ro
    depends_on:
      - php
    networks:
      - moodle-net

  mysql:
    image: mysql:8.0
    container_name: moodle-mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - ./mysql/custom.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - moodle-mysql-data:/var/lib/mysql
    networks:
      - moodle-net

volumes:
  moodle-mysql-data:

networks:
  moodle-net:
    driver: bridge