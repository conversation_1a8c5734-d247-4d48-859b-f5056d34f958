# All sensitive/environment-specific values are loaded from a .env file at the project root.
services:
  php:
    build: ./php
    env_file:
      - .env
    environment:
      - MOODLE_DB_HOST=${MOODLE_DB_HOST}
      - <PERSON><PERSON><PERSON>LE_DB_NAME=${MO<PERSON>LE_DB_NAME}
      - MOODLE_DB_USER=${MOODLE_DB_USER}
      - MOODLE_DB_PASS=${MOODLE_DB_PASS}
      - MOODLE_DB_PREFIX=${MOODLE_DB_PREFIX}
      - MOODLE_DB_PORT=${MOODLE_DB_PORT}
      - MOODLE_WWWROOT=${MOODLE_WWWROOT}
      - MOODLE_DATAROOT=${MOODLE_DATAROOT}
      - MOODLE_ADMIN=${MOODLE_ADMIN}
    container_name: moodle-php
    volumes:
      - ./php/custom-configs/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./php/custom-configs/php-fpm.conf:/usr/local/etc/php-fpm.d/php-fpm.conf
      - ./php/moodledata:/var/moodledata
      - ./moodle:/var/www/html
    networks:
      - moodle-net
    depends_on:
      - mysql

  nginx:
    image: nginx:latest
    container_name: moodle-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/certs:/etc/nginx/certs:ro
      - ./moodle:/var/www/html:ro
      - ./php/moodledata:/var/moodledata:ro
    depends_on:
      - php
    networks:
      - moodle-net

  mysql:
    image: mysql:8.0
    container_name: moodle-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - ./mysql/custom.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - moodle-mysql-data:/var/lib/mysql
    networks:
      - moodle-net

volumes:
  moodle-mysql-data:

networks:
  moodle-net:
    driver: bridge