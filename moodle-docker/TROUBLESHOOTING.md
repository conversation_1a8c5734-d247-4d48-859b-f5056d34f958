# Moodle Docker Troubleshooting Guide

## Issues Fixed

### 1. Environment Variable Inconsistencies
**Problem**: Database configuration mismatches between MySQL and Moodle settings.

**Fixed**:
- ✅ Corrected database name: `FabeamU` → `FatbeamU`
- ✅ Aligned database users: `MYSQL_USER` and `MOODLE_DB_USER` now both use `FatbeamU_admin`
- ✅ Removed leading spaces from all MOODLE_* variables
- ✅ Added comments for better organization

### 2. Docker Compose Configuration
**Problem**: Environment variables not properly passed to PHP container.

**Fixed**:
- ✅ Added explicit environment variable mapping in docker-compose.yml
- ✅ Ensured all MOODLE_* variables are available to the PHP container

## Verification Steps

1. **Run the configuration verification script**:
   ```bash
   cd moodle-docker
   ./verify-config.sh
   ```

2. **Check for any remaining issues** in the output and fix them if needed.

## Starting Moodle

1. **Stop any running containers**:
   ```bash
   cd moodle-docker
   docker-compose down
   ```

2. **Start with fresh build**:
   ```bash
   docker-compose up --build
   ```

3. **Access Moodle**:
   - Open your browser to: https://localhost
   - Accept the self-signed certificate warning
   - Follow the Moodle installation wizard

## Common Issues and Solutions

### Issue: "Database connection failed"
**Cause**: Database credentials mismatch or database not ready.

**Solutions**:
1. Verify credentials match in .env file using `./verify-config.sh`
2. Wait for MySQL to fully start (check logs: `docker-compose logs mysql`)
3. Ensure database name exists: `docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"`

### Issue: "Cannot access https://localhost"
**Cause**: SSL certificate issues or nginx not running.

**Solutions**:
1. Check nginx is running: `docker-compose ps`
2. Check nginx logs: `docker-compose logs nginx`
3. Verify SSL certificates exist: `ls -la nginx/certs/`
4. Try HTTP first: temporarily change MOODLE_WWWROOT to `http://localhost`

### Issue: "Moodle data directory not writable"
**Cause**: Permission issues with moodledata directory.

**Solutions**:
1. Check directory exists: `ls -la php/moodledata/`
2. Fix permissions: `chmod -R 777 php/moodledata/`
3. Restart containers: `docker-compose restart`

### Issue: "Environment variables not loading"
**Cause**: Formatting issues in .env file.

**Solutions**:
1. Check for spaces around = signs
2. Ensure no leading/trailing spaces on variable names
3. Use `./verify-config.sh` to check formatting

## Environment Variables Reference

### MySQL Variables
- `MYSQL_ROOT_PASSWORD`: Root password for MySQL
- `MYSQL_DATABASE`: Database name (must match MOODLE_DB_NAME)
- `MYSQL_USER`: Database user (must match MOODLE_DB_USER)
- `MYSQL_PASSWORD`: Database password (must match MOODLE_DB_PASS)

### Moodle Variables
- `MOODLE_DB_HOST`: Database host (usually 'mysql' for Docker)
- `MOODLE_DB_NAME`: Database name (must match MYSQL_DATABASE)
- `MOODLE_DB_USER`: Database user (must match MYSQL_USER)
- `MOODLE_DB_PASS`: Database password (must match MYSQL_PASSWORD)
- `MOODLE_DB_PREFIX`: Table prefix (default: 'mdl_')
- `MOODLE_DB_PORT`: Database port (default: 3306)
- `MOODLE_WWWROOT`: Full URL to access Moodle (e.g., https://localhost)
- `MOODLE_DATAROOT`: Path to data directory (default: /var/moodledata)
- `MOODLE_ADMIN`: Admin directory name (default: admin)

## Logs and Debugging

### View container logs:
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs nginx
docker-compose logs php
docker-compose logs mysql
```

### Access container shell:
```bash
# PHP container
docker-compose exec php bash

# MySQL container
docker-compose exec mysql bash
```

### Check Moodle configuration:
```bash
# View current config
docker-compose exec php cat /var/www/html/config.php

# Check environment variables in PHP container
docker-compose exec php env | grep MOODLE
```

## Next Steps After Setup

1. Complete Moodle installation wizard
2. Configure site settings
3. Set up user accounts
4. Install additional plugins if needed
5. Configure backup schedules

For additional help, check the Moodle documentation at: https://docs.moodle.org/
