#!/bin/bash

# Moodle Session Timeout Fix Script
# This script applies common fixes for persistent session timeout issues

echo "=== Moodle Session Timeout Fix ==="
echo

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: docker-compose.yml not found!"
    echo "Please run this script from the moodle-docker directory."
    exit 1
fi

echo "🔧 Applying session timeout fixes..."
echo

# Step 1: Restart containers to apply session configuration
echo "1. Restarting containers to apply session configuration..."
docker-compose down
sleep 2
docker-compose up -d
echo "   ✅ Containers restarted"
echo

# Step 2: Wait for services to be ready
echo "2. Waiting for services to initialize..."
sleep 15
echo "   ✅ Services should be ready"
echo

# Step 3: Check if containers are running
echo "3. Checking container status..."
if docker-compose ps | grep -q "Up"; then
    echo "   ✅ Containers are running"
else
    echo "   ❌ Some containers may not be running properly"
    docker-compose ps
fi
echo

# Step 4: Verify session configuration
echo "4. Verifying session configuration..."
if docker-compose exec -T php php -r "echo 'Session timeout: ' . ini_get('session.gc_maxlifetime') . ' seconds\n';" 2>/dev/null; then
    echo "   ✅ PHP session configuration applied"
else
    echo "   ❌ Could not verify PHP session configuration"
fi
echo

# Step 5: Instructions for user
echo "=== Next Steps ==="
echo
echo "1. 🌐 **Clear your browser cache and cookies** for localhost"
echo "2. 🔄 **Try accessing Moodle in incognito/private mode**"
echo "3. 🔗 **Access Moodle at**: https://localhost"
echo "4. 🔧 **If still having issues, try HTTP instead**: http://localhost"
echo "   (Change MOODLE_WWWROOT in .env file to http://localhost)"
echo
echo "=== Session Configuration Applied ==="
echo "• Session timeout: 8 hours (28800 seconds)"
echo "• Session warning: 20 minutes (1200 seconds)"
echo "• Session cookie: MoodleSession"
echo "• Cookie path: /"
echo "• Cookie security: Configured for localhost"
echo
echo "If you continue to experience session timeouts:"
echo "1. Check browser developer tools for cookie issues"
echo "2. Verify no browser extensions are blocking cookies"
echo "3. Try a different browser"
echo "4. Check the TROUBLESHOOTING.md file for more solutions"
echo
echo "=== Fix Complete ==="
