FROM php:8.1-fpm

# Install required PHP extensions for Moodle
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libxml2-dev \
    libicu-dev \
    libcurl4-openssl-dev \
    libonig-dev \
    libsodium-dev \
    zip \
    unzip \
    git \
    cron \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        gd \
        mysqli \
        zip \
        intl \
        soap \
        exif \
        opcache \
        curl \
        dom \
        simplexml \
        mbstring \
        sodium \
        xmlrpc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set recommended PHP.ini settings
COPY custom-configs/php.ini /usr/local/etc/php/conf.d/php.ini
COPY custom-configs/php-fpm.conf /usr/local/etc/php-fpm.d/php-fpm.conf

# Setup Moodle cron job
COPY cron/moodle-cron /etc/cron.d/moodle-cron
RUN chmod 0644 /etc/cron.d/moodle-cron \
    && crontab /etc/cron.d/moodle-cron

# Create startup script
COPY scripts/start.sh /start.sh
RUN chmod +x /start.sh

WORKDIR /var/www/html

CMD ["/start.sh"]