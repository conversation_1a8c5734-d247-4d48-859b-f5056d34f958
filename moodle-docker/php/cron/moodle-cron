# Moodle cron job - runs every minute
# This is the recommended frequency for Moodle cron
* * * * * www-data /usr/local/bin/php /var/www/html/admin/cli/cron.php >/dev/null 2>&1

# Alternative: Run every 5 minutes (less frequent, but still acceptable)
# */5 * * * * www-data /usr/local/bin/php /var/www/html/admin/cli/cron.php >/dev/null 2>&1

# Log cron execution (uncomment for debugging)
# * * * * * www-data /usr/local/bin/php /var/www/html/admin/cli/cron.php >> /var/log/moodle-cron.log 2>&1
