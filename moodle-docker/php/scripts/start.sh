#!/bin/bash

# Moodle PHP-FPM Startup Script
# This script starts both cron and PHP-FPM services

echo "Starting Moodle PHP-FPM container..."

# Start cron service
echo "Starting cron service..."
service cron start

# Ensure proper permissions for moodledata
echo "Setting moodledata permissions..."
chown -R www-data:www-data /var/moodledata
chmod -R 755 /var/moodledata

# Ensure proper permissions for Moodle files
echo "Setting Moodle file permissions..."
chown -R www-data:www-data /var/www/html
find /var/www/html -type f -exec chmod 644 {} \;
find /var/www/html -type d -exec chmod 755 {} \;

# Make sure config.php is readable
if [ -f /var/www/html/config.php ]; then
    chmod 644 /var/www/html/config.php
    echo "Config.php permissions set"
fi

# Start PHP-FPM in foreground
echo "Starting PHP-FPM..."
exec php-fpm
