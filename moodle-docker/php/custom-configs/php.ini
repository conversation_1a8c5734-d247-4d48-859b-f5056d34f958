; Core PHP Settings for Moodle 4.2+
memory_limit = 512M
max_execution_time = 300
max_input_vars = 5000
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20
date.timezone = America/Boise

; Required Moodle Settings
file_uploads = On
allow_url_fopen = On
magic_quotes_runtime = Off

; OPcache settings
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1

; Security settings
expose_php = Off
display_errors = Off
log_errors = On

; Session settings for Moodle
session.auto_start = 0
session.save_handler = files
session.save_path = "/tmp"
session.use_cookies = 1
session.use_only_cookies = 1
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_secure = 0
session.cookie_httponly = 1
session.cookie_samesite = "Lax"
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 28800
session.name = MoodleSession