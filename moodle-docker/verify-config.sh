#!/bin/bash

# Moodle Docker Configuration Verification Script
# This script helps verify that your environment variables are properly set

echo "=== Moodle Docker Configuration Verification ==="
echo

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ ERROR: .env file not found!"
    exit 1
fi

echo "✅ .env file found"
echo

# Load environment variables
set -a
source .env
set +a

echo "=== Environment Variables ==="
echo "MySQL Configuration:"
echo "  MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:+[SET]}"
echo "  MYSQL_DATABASE: $MYSQL_DATABASE"
echo "  MYSQL_USER: $MYSQL_USER"
echo "  MYSQL_PASSWORD: ${MYSQL_PASSWORD:+[SET]}"
echo

echo "Moodle Configuration:"
echo "  MOODLE_DB_HOST: $MOODLE_DB_HOST"
echo "  MOODLE_DB_NAME: $MOODLE_DB_NAME"
echo "  MOODLE_DB_USER: $MOODLE_DB_USER"
echo "  MOODLE_DB_PASS: ${MOODLE_DB_PASS:+[SET]}"
echo "  MOODLE_DB_PREFIX: $MOODLE_DB_PREFIX"
echo "  MOODLE_DB_PORT: $MOODLE_DB_PORT"
echo "  MOODLE_WWWROOT: $MOODLE_WWWROOT"
echo "  MOODLE_DATAROOT: $MOODLE_DATAROOT"
echo "  MOODLE_ADMIN: $MOODLE_ADMIN"
echo
echo "Session Configuration:"
echo "  MOODLE_SESSION_TIMEOUT: $MOODLE_SESSION_TIMEOUT"
echo "  MOODLE_SESSION_WARNING: $MOODLE_SESSION_WARNING"
echo "  MOODLE_SESSION_COOKIE: $MOODLE_SESSION_COOKIE"
echo

# Check for consistency
echo "=== Configuration Consistency Check ==="

# Check database name consistency
if [ "$MYSQL_DATABASE" = "$MOODLE_DB_NAME" ]; then
    echo "✅ Database names match: $MYSQL_DATABASE"
else
    echo "❌ Database name mismatch:"
    echo "   MySQL: $MYSQL_DATABASE"
    echo "   Moodle: $MOODLE_DB_NAME"
fi

# Check user consistency
if [ "$MYSQL_USER" = "$MOODLE_DB_USER" ]; then
    echo "✅ Database users match: $MYSQL_USER"
else
    echo "❌ Database user mismatch:"
    echo "   MySQL: $MYSQL_USER"
    echo "   Moodle: $MOODLE_DB_USER"
fi

# Check password consistency
if [ "$MYSQL_PASSWORD" = "$MOODLE_DB_PASS" ]; then
    echo "✅ Database passwords match"
else
    echo "❌ Database password mismatch"
fi

echo

# Check required files
echo "=== Required Files Check ==="

files_to_check=(
    "docker-compose.yml"
    "nginx/nginx.conf"
    "nginx/conf.d/default.conf"
    "nginx/certs/selfsigned.crt"
    "nginx/certs/selfsigned.key"
    "php/Dockerfile"
    "moodle/config.php"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
    fi
done

echo

# Check for common issues
echo "=== Common Issues Check ==="

# Check for leading/trailing spaces in variables
if echo "$MYSQL_USER" | grep -q "^[[:space:]]\|[[:space:]]$"; then
    echo "❌ MYSQL_USER has leading/trailing spaces"
else
    echo "✅ MYSQL_USER has no leading/trailing spaces"
fi

if echo "$MOODLE_DB_USER" | grep -q "^[[:space:]]\|[[:space:]]$"; then
    echo "❌ MOODLE_DB_USER has leading/trailing spaces"
else
    echo "✅ MOODLE_DB_USER has no leading/trailing spaces"
fi

# Check WWWROOT format
if [[ "$MOODLE_WWWROOT" =~ ^https?://[^[:space:]]+$ ]]; then
    echo "✅ MOODLE_WWWROOT format is valid: $MOODLE_WWWROOT"
else
    echo "❌ MOODLE_WWWROOT format may be invalid: $MOODLE_WWWROOT"
fi

echo
echo "=== Verification Complete ==="
echo
echo "To start your Moodle instance:"
echo "  docker-compose down"
echo "  docker-compose up --build"
echo
echo "Then access: $MOODLE_WWWROOT"
