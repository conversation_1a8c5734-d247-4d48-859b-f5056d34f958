<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace core_adminpresets\local\setting;

/**
 * Used to validate a textarea used for ip addresses.
 *
 * @package          core_adminpresets
 * @copyright        2021 Pimenko <<EMAIL>><pimenko.com>
 * <AUTHOR> | Sylva<PERSON> | <PERSON><PERSON> based on <PERSON> <<EMAIL>> code
 * @license          http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class adminpresets_admin_setting_configiplist extends adminpresets_admin_setting_configtext {

    protected function set_value($value) {
        // Check ip format.
        if ($this->settingdata->validate($value) !== true) {
            $this->value = false;
            $this->set_visiblevalue();
            return false;
        }
        $this->value = $value;
        $this->set_visiblevalue();

        return true;
    }
}
