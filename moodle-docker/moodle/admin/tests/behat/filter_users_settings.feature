@core @core_admin @javascript
Feature: An administrator can configure the available user list filters
  In order to have all needed user filters instantly at hand
  As an admin
  I want to configure the filters which are shown as default

  Scenario: Do not change the user filter default configuration
    When I log in as "admin"
    And I navigate to "Users > Accounts > Browse list of users" in site administration
    Then I should see "User full name"
    And I should not see "Last name" in the "New filter" "fieldset"
    And I should not see "Firstname" in the "New filter" "fieldset"
    And I should not see "Username" in the "New filter" "fieldset"
    And I should not see "Email address" in the "New filter" "fieldset"
    And I should not see "City/town" in the "New filter" "fieldset"
    And I should not see "Country" in the "New filter" "fieldset"
    And I should not see "Confirmed" in the "New filter" "fieldset"
    And I should not see "Suspended account" in the "New filter" "fieldset"
    And I should not see "User profile fields" in the "New filter" "fieldset"
    And I should not see "Course role" in the "New filter" "fieldset"
    And I should not see "Enrolled in any course" in the "New filter" "fieldset"
    And I should not see "System role" in the "New filter" "fieldset"
    And I should not see "Cohort ID" in the "New filter" "fieldset"
    And I should not see "First access" in the "New filter" "fieldset"
    And I should not see "Last access" in the "New filter" "fieldset"
    And I should not see "Last modified" in the "New filter" "fieldset"
    And I should not see "Authentication" in the "New filter" "fieldset"
    And I should not see "ID number" in the "New filter" "fieldset"
    And I should not see "Last IP address" in the "New filter" "fieldset"
    And I should not see "MNet ID provider" in the "New filter" "fieldset"
    And I navigate to "Users > Accounts > Bulk user actions" in site administration
    Then I should see "User full name"
    And I should not see "Last name" in the "New filter" "fieldset"
    And I should not see "Firstname" in the "New filter" "fieldset"
    And I should not see "Username" in the "New filter" "fieldset"
    And I should not see "Email address" in the "New filter" "fieldset"
    And I should not see "City/town" in the "New filter" "fieldset"
    And I should not see "Country" in the "New filter" "fieldset"
    And I should not see "Confirmed" in the "New filter" "fieldset"
    And I should not see "Suspended account" in the "New filter" "fieldset"
    And I should not see "User profile fields" in the "New filter" "fieldset"
    And I should not see "Course role" in the "New filter" "fieldset"
    And I should not see "Enrolled in any course" in the "New filter" "fieldset"
    And I should not see "System role" in the "New filter" "fieldset"
    And I should not see "Cohort ID" in the "New filter" "fieldset"
    And I should not see "First access" in the "New filter" "fieldset"
    And I should not see "Last access" in the "New filter" "fieldset"
    And I should not see "Last modified" in the "New filter" "fieldset"
    And I should not see "Authentication" in the "New filter" "fieldset"
    And I should not see "ID number" in the "New filter" "fieldset"
    And I should not see "Last IP address" in the "New filter" "fieldset"
    And I should not see "MNet ID provider" in the "New filter" "fieldset"

  Scenario: Change the user filter default configuration to something else
    Given the following config values are set as admin:
      | userfiltersdefault | realname,username,email |
    And I log in as "admin"
    And I navigate to "Users > Accounts > Browse list of users" in site administration
    Then I should see "User full name"
    And I should not see "Last name" in the "New filter" "fieldset"
    And I should not see "Firstname" in the "New filter" "fieldset"
    And I should see "Username" in the "New filter" "fieldset"
    And I should see "Email address" in the "New filter" "fieldset"
    And I should not see "City/town" in the "New filter" "fieldset"
    And I should not see "Country" in the "New filter" "fieldset"
    And I should not see "Confirmed" in the "New filter" "fieldset"
    And I should not see "Suspended account" in the "New filter" "fieldset"
    And I should not see "User profile fields" in the "New filter" "fieldset"
    And I should not see "Course role" in the "New filter" "fieldset"
    And I should not see "Enrolled in any course" in the "New filter" "fieldset"
    And I should not see "System role" in the "New filter" "fieldset"
    And I should not see "Cohort ID" in the "New filter" "fieldset"
    And I should not see "First access" in the "New filter" "fieldset"
    And I should not see "Last access" in the "New filter" "fieldset"
    And I should not see "Last modified" in the "New filter" "fieldset"
    And I should not see "Authentication" in the "New filter" "fieldset"
    And I should not see "ID number" in the "New filter" "fieldset"
    And I should not see "Last IP address" in the "New filter" "fieldset"
    And I should not see "MNet ID provider" in the "New filter" "fieldset"
    And I navigate to "Users > Accounts > Bulk user actions" in site administration
    Then I should see "User full name"
    And I should not see "Last name" in the "New filter" "fieldset"
    And I should not see "Firstname" in the "New filter" "fieldset"
    And I should see "Username" in the "New filter" "fieldset"
    And I should see "Email address" in the "New filter" "fieldset"
    And I should not see "City/town" in the "New filter" "fieldset"
    And I should not see "Country" in the "New filter" "fieldset"
    And I should not see "Confirmed" in the "New filter" "fieldset"
    And I should not see "Suspended account" in the "New filter" "fieldset"
    And I should not see "User profile fields" in the "New filter" "fieldset"
    And I should not see "Course role" in the "New filter" "fieldset"
    And I should not see "Enrolled in any course" in the "New filter" "fieldset"
    And I should not see "System role" in the "New filter" "fieldset"
    And I should not see "Cohort ID" in the "New filter" "fieldset"
    And I should not see "First access" in the "New filter" "fieldset"
    And I should not see "Last access" in the "New filter" "fieldset"
    And I should not see "Last modified" in the "New filter" "fieldset"
    And I should not see "Authentication" in the "New filter" "fieldset"
    And I should not see "ID number" in the "New filter" "fieldset"
    And I should not see "Last IP address" in the "New filter" "fieldset"
    And I should not see "MNet ID provider" in the "New filter" "fieldset"

  Scenario: Change the user filter default configuration to no filter at all
    Given the following config values are set as admin:
      | userfiltersdefault | |
    And I log in as "admin"
    And I navigate to "Users > Accounts > Browse list of users" in site administration
    Then I should see "User full name"
    And I should not see "Last name" in the "New filter" "fieldset"
    And I should not see "Firstname" in the "New filter" "fieldset"
    And I should not see "Username" in the "New filter" "fieldset"
    And I should not see "Email address" in the "New filter" "fieldset"
    And I should not see "City/town" in the "New filter" "fieldset"
    And I should not see "Country" in the "New filter" "fieldset"
    And I should not see "Confirmed" in the "New filter" "fieldset"
    And I should not see "Suspended account" in the "New filter" "fieldset"
    And I should not see "User profile fields" in the "New filter" "fieldset"
    And I should not see "Course role" in the "New filter" "fieldset"
    And I should not see "Enrolled in any course" in the "New filter" "fieldset"
    And I should not see "System role" in the "New filter" "fieldset"
    And I should not see "Cohort ID" in the "New filter" "fieldset"
    And I should not see "First access" in the "New filter" "fieldset"
    And I should not see "Last access" in the "New filter" "fieldset"
    And I should not see "Last modified" in the "New filter" "fieldset"
    And I should not see "Authentication" in the "New filter" "fieldset"
    And I should not see "ID number" in the "New filter" "fieldset"
    And I should not see "Last IP address" in the "New filter" "fieldset"
    And I should not see "MNet ID provider" in the "New filter" "fieldset"
    And I navigate to "Users > Accounts > Bulk user actions" in site administration
    Then I should see "User full name"
    And I should not see "Last name" in the "New filter" "fieldset"
    And I should not see "Firstname" in the "New filter" "fieldset"
    And I should not see "Username" in the "New filter" "fieldset"
    And I should not see "Email address" in the "New filter" "fieldset"
    And I should not see "City/town" in the "New filter" "fieldset"
    And I should not see "Country" in the "New filter" "fieldset"
    And I should not see "Confirmed" in the "New filter" "fieldset"
    And I should not see "Suspended account" in the "New filter" "fieldset"
    And I should not see "User profile fields" in the "New filter" "fieldset"
    And I should not see "Course role" in the "New filter" "fieldset"
    And I should not see "Enrolled in any course" in the "New filter" "fieldset"
    And I should not see "System role" in the "New filter" "fieldset"
    And I should not see "Cohort ID" in the "New filter" "fieldset"
    And I should not see "First access" in the "New filter" "fieldset"
    And I should not see "Last access" in the "New filter" "fieldset"
    And I should not see "Last modified" in the "New filter" "fieldset"
    And I should not see "Authentication" in the "New filter" "fieldset"
    And I should not see "ID number" in the "New filter" "fieldset"
    And I should not see "Last IP address" in the "New filter" "fieldset"
    And I should not see "MNet ID provider" in the "New filter" "fieldset"
