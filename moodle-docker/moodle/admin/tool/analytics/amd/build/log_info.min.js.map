{"version": 3, "file": "log_info.min.js", "sources": ["../src/log_info.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Shows a dialogue with info about this logs.\n *\n * @module     tool_analytics/log_info\n * @copyright  2017 <PERSON> {@link http://www.davidmonllao.com}\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine(['jquery', 'core/str', 'core/modal_factory', 'core/notification'], function($, str, ModalFactory, Notification) {\n\n    return /** @alias module:tool_analytics/log_info */ {\n\n        /**\n         * Prepares a modal info for a log's results.\n         *\n         * @method loadInfo\n         * @param {int} id\n         * @param {string[]} info\n         */\n        loadInfo: function(id, info) {\n\n            var link = $('[data-model-log-id=\"' + id + '\"]');\n            str.get_string('loginfo', 'tool_analytics').then(function(langString) {\n\n                var bodyInfo = $(\"<ul>\");\n                info.forEach(function(item) {\n                    bodyInfo.append('<li>' + item + '</li>');\n                });\n                bodyInfo.append(\"</ul>\");\n\n                return ModalFactory.create({\n                    title: langString,\n                    body: bodyInfo.html(),\n                    large: true,\n                }, link);\n\n            }).catch(Notification.exception);\n        }\n    };\n});\n"], "names": ["define", "$", "str", "ModalFactory", "Notification", "loadInfo", "id", "info", "link", "get_string", "then", "langString", "bodyInfo", "for<PERSON>ach", "item", "append", "create", "title", "body", "html", "large", "catch", "exception"], "mappings": ";;;;;;;AAsBAA,iCAAO,CAAC,SAAU,WAAY,qBAAsB,sBAAsB,SAASC,EAAGC,IAAKC,aAAcC,oBAEjD,CAShDC,SAAU,SAASC,GAAIC,UAEfC,KAAOP,EAAE,uBAAyBK,GAAK,MAC3CJ,IAAIO,WAAW,UAAW,kBAAkBC,MAAK,SAASC,gBAElDC,SAAWX,EAAE,eACjBM,KAAKM,SAAQ,SAASC,MAClBF,SAASG,OAAO,OAASD,KAAO,YAEpCF,SAASG,OAAO,SAETZ,aAAaa,OAAO,CACvBC,MAAON,WACPO,KAAMN,SAASO,OACfC,OAAO,GACRZ,SAEJa,MAAMjB,aAAakB"}