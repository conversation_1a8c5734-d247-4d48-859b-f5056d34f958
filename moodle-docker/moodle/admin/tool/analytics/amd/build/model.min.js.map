{"version": 3, "file": "model.min.js", "sources": ["../src/model.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * AMD module for model actions confirmation.\n *\n * @module     tool_analytics/model\n * @copyright  2017 David <PERSON>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine(['jquery', 'core/str', 'core/log', 'core/notification', 'core/modal_factory', 'core/modal_events', 'core/templates'],\n    function($, Str, log, Notification, ModalFactory, ModalEvents, Templates) {\n\n    /**\n     * List of actions that require confirmation and confirmation message.\n     */\n    var actionsList = {\n        clear: {\n            title: {\n                key: 'clearpredictions',\n                component: 'tool_analytics'\n            }, body: {\n                key: 'clearmodelpredictions',\n                component: 'tool_analytics'\n            }\n\n        },\n        'delete': {\n            title: {\n                key: 'delete',\n                component: 'tool_analytics'\n            }, body: {\n                key: 'deletemodelconfirmation',\n                component: 'tool_analytics'\n            }\n        }\n    };\n\n    /**\n     * Returns the model name.\n     *\n     * @param {Object} actionItem The action item DOM node.\n     * @return {String}\n     */\n    var getModelName = function(actionItem) {\n        var wrap = $(actionItem).closest('[data-model-name]');\n\n        if (wrap.length) {\n            return wrap.attr('data-model-name');\n\n        } else {\n            log.error('Unexpected DOM error - unable to obtain the model name');\n            return '';\n        }\n    };\n\n    /** @alias module:tool_analytics/model */\n    return {\n\n        /**\n         * Displays a confirm modal window before executing the action.\n         *\n         * @param {String} actionId\n         * @param {String} actionType\n         */\n        confirmAction: function(actionId, actionType) {\n            $('[data-action-id=\"' + actionId + '\"]').on('click', function(ev) {\n                ev.preventDefault();\n\n                var a = $(ev.currentTarget);\n\n                if (typeof actionsList[actionType] === \"undefined\") {\n                    log.error('Action \"' + actionType + '\" is not allowed.');\n                    return;\n                }\n\n                var reqStrings = [\n                    actionsList[actionType].title,\n                    actionsList[actionType].body\n                ];\n                reqStrings[1].param = getModelName(a);\n\n                var stringsPromise = Str.get_strings(reqStrings);\n                var modalPromise = ModalFactory.create({type: ModalFactory.types.SAVE_CANCEL});\n\n                $.when(stringsPromise, modalPromise).then(function(strings, modal) {\n                    modal.setTitle(strings[0]);\n                    modal.setBody(strings[1]);\n                    modal.setSaveButtonText(strings[0]);\n                    modal.getRoot().on(ModalEvents.save, function() {\n                        window.location.href = a.attr('href');\n                    });\n                    modal.show();\n                    return modal;\n                }).fail(Notification.exception);\n            });\n        },\n\n        /**\n         * Displays evaluation mode and time-splitting method choices.\n         *\n         * @param  {String}  actionId\n         * @param  {Boolean} trainedOnlyExternally\n         */\n        selectEvaluationOptions: function(actionId, trainedOnlyExternally) {\n            $('[data-action-id=\"' + actionId + '\"]').on('click', function(ev) {\n                ev.preventDefault();\n\n                var a = $(ev.currentTarget);\n\n                var timeSplittingMethods = $(this).attr('data-timesplitting-methods');\n\n                var stringsPromise = Str.get_strings([\n                    {\n                        key: 'evaluatemodel',\n                        component: 'tool_analytics'\n                    }, {\n                        key: 'evaluate',\n                        component: 'tool_analytics'\n                    }\n                ]);\n                var modalPromise = ModalFactory.create({type: ModalFactory.types.SAVE_CANCEL});\n                var bodyPromise = Templates.render('tool_analytics/evaluation_options', {\n                    trainedexternally: trainedOnlyExternally,\n                    timesplittingmethods: JSON.parse(timeSplittingMethods)\n                });\n\n                $.when(stringsPromise, modalPromise).then(function(strings, modal) {\n\n\n                    modal.getRoot().on(ModalEvents.hidden, modal.destroy.bind(modal));\n\n                    modal.setTitle(strings[0]);\n                    modal.setSaveButtonText(strings[1]);\n                    modal.setBody(bodyPromise);\n\n                    modal.getRoot().on(ModalEvents.save, function() {\n\n                        // Evaluation mode.\n                        var evaluationMode = $(\"input[name='evaluationmode']:checked\").val();\n                        if (evaluationMode == 'trainedmodel') {\n                            a.attr('href', a.attr('href') + '&mode=trainedmodel');\n                        }\n\n                        // Selected time-splitting id.\n                        var timeSplittingMethod = $(\"#id-evaluation-timesplitting\").val();\n                        a.attr('href', a.attr('href') + '&timesplitting=' + timeSplittingMethod);\n\n                        window.location.href = a.attr('href');\n                        return;\n                    });\n\n                    modal.show();\n                    return modal;\n                }).fail(Notification.exception);\n            });\n        },\n\n        /**\n         * Displays export options.\n         *\n         * We have two main options: export training data and export configuration.\n         * The 2nd option has an extra option: include the trained algorithm weights.\n         *\n         * @param  {String}  actionId\n         * @param  {Boolean} isTrained\n         */\n        selectExportOptions: function(actionId, isTrained) {\n            $('[data-action-id=\"' + actionId + '\"]').on('click', function(ev) {\n                ev.preventDefault();\n\n                var a = $(ev.currentTarget);\n\n                if (!isTrained) {\n                    // Export the model configuration if the model is not trained. We can't export anything else.\n                    a.attr('href', a.attr('href') + '&action=exportmodel&includeweights=0');\n                    window.location.href = a.attr('href');\n                    return;\n                }\n\n                var stringsPromise = Str.get_strings([\n                    {\n                        key: 'export',\n                        component: 'tool_analytics'\n                    }\n                ]);\n                var modalPromise = ModalFactory.create({type: ModalFactory.types.SAVE_CANCEL});\n                var bodyPromise = Templates.render('tool_analytics/export_options', {});\n\n                $.when(stringsPromise, modalPromise).then(function(strings, modal) {\n\n                    modal.getRoot().on(ModalEvents.hidden, modal.destroy.bind(modal));\n\n                    modal.setTitle(strings[0]);\n                    modal.setSaveButtonText(strings[0]);\n                    modal.setBody(bodyPromise);\n\n                    modal.getRoot().on(ModalEvents.save, function() {\n\n                        var exportOption = $(\"input[name='exportoption']:checked\").val();\n\n                        if (exportOption == 'exportdata') {\n                            a.attr('href', a.attr('href') + '&action=exportdata');\n\n                        } else {\n                            a.attr('href', a.attr('href') + '&action=exportmodel');\n                            if ($(\"#id-includeweights\").is(':checked')) {\n                                a.attr('href', a.attr('href') + '&includeweights=1');\n                            } else {\n                                a.attr('href', a.attr('href') + '&includeweights=0');\n                            }\n                        }\n\n                        window.location.href = a.attr('href');\n                        return;\n                    });\n\n                    modal.show();\n                    return modal;\n                }).fail(Notification.exception);\n            });\n        }\n    };\n});\n"], "names": ["define", "$", "Str", "log", "Notification", "ModalFactory", "ModalEvents", "Templates", "actionsList", "clear", "title", "key", "component", "body", "confirmAction", "actionId", "actionType", "on", "ev", "preventDefault", "a", "currentTarget", "wrap", "reqStrings", "param", "closest", "length", "attr", "error", "stringsPromise", "get_strings", "modalPromise", "create", "type", "types", "SAVE_CANCEL", "when", "then", "strings", "modal", "setTitle", "setBody", "setSaveButtonText", "getRoot", "save", "window", "location", "href", "show", "fail", "exception", "selectEvaluationOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeSplittingMethods", "this", "bodyPromise", "render", "trainedexternally", "timesplittingmethods", "JSON", "parse", "hidden", "destroy", "bind", "val", "timeSplittingMethod", "selectExportOptions", "isTrained", "is"], "mappings": ";;;;;;;AAsBAA,8BAAO,CAAC,SAAU,WAAY,WAAY,oBAAqB,qBAAsB,oBAAqB,mBACtG,SAASC,EAAGC,IAAKC,IAAKC,aAAcC,aAAcC,YAAaC,eAK3DC,YAAc,CACdC,MAAO,CACHC,MAAO,CACHC,IAAK,mBACLC,UAAW,kBACZC,KAAM,CACLF,IAAK,wBACLC,UAAW,0BAIT,CACNF,MAAO,CACHC,IAAK,SACLC,UAAW,kBACZC,KAAM,CACLF,IAAK,0BACLC,UAAW,0BAwBhB,CAQHE,cAAe,SAASC,SAAUC,YAC9Bf,EAAE,oBAAsBc,SAAW,MAAME,GAAG,SAAS,SAASC,IAC1DA,GAAGC,qBAECC,EAAInB,EAAEiB,GAAGG,uBAE0B,IAA5Bb,YAAYQ,iBA1B3BM,KA+BQC,WAAa,CACbf,YAAYQ,YAAYN,MACxBF,YAAYQ,YAAYH,MAE5BU,WAAW,GAAGC,OAnClBF,KAAOrB,EAmCgCmB,GAnClBK,QAAQ,sBAExBC,OACEJ,KAAKK,KAAK,oBAGjBxB,IAAIyB,MAAM,0DACH,QA8BCC,eAAiB3B,IAAI4B,YAAYP,YACjCQ,aAAe1B,aAAa2B,OAAO,CAACC,KAAM5B,aAAa6B,MAAMC,cAEjElC,EAAEmC,KAAKP,eAAgBE,cAAcM,MAAK,SAASC,QAASC,cACxDA,MAAMC,SAASF,QAAQ,IACvBC,MAAME,QAAQH,QAAQ,IACtBC,MAAMG,kBAAkBJ,QAAQ,IAChCC,MAAMI,UAAU1B,GAAGX,YAAYsC,MAAM,WACjCC,OAAOC,SAASC,KAAO3B,EAAEO,KAAK,WAElCY,MAAMS,OACCT,SACRU,KAAK7C,aAAa8C,gBAtBjB/C,IAAIyB,MAAM,WAAaZ,WAAa,yBAgChDmC,wBAAyB,SAASpC,SAAUqC,uBACxCnD,EAAE,oBAAsBc,SAAW,MAAME,GAAG,SAAS,SAASC,IAC1DA,GAAGC,qBAECC,EAAInB,EAAEiB,GAAGG,eAETgC,qBAAuBpD,EAAEqD,MAAM3B,KAAK,8BAEpCE,eAAiB3B,IAAI4B,YAAY,CACjC,CACInB,IAAK,gBACLC,UAAW,kBACZ,CACCD,IAAK,WACLC,UAAW,oBAGfmB,aAAe1B,aAAa2B,OAAO,CAACC,KAAM5B,aAAa6B,MAAMC,cAC7DoB,YAAchD,UAAUiD,OAAO,oCAAqC,CACpEC,kBAAmBL,sBACnBM,qBAAsBC,KAAKC,MAAMP,wBAGrCpD,EAAEmC,KAAKP,eAAgBE,cAAcM,MAAK,SAASC,QAASC,cAGxDA,MAAMI,UAAU1B,GAAGX,YAAYuD,OAAQtB,MAAMuB,QAAQC,KAAKxB,QAE1DA,MAAMC,SAASF,QAAQ,IACvBC,MAAMG,kBAAkBJ,QAAQ,IAChCC,MAAME,QAAQc,aAEdhB,MAAMI,UAAU1B,GAAGX,YAAYsC,MAAM,WAIX,gBADD3C,EAAE,wCAAwC+D,OAE3D5C,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,0BAIhCsC,oBAAsBhE,EAAE,gCAAgC+D,MAC5D5C,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,kBAAoBsC,qBAEpDpB,OAAOC,SAASC,KAAO3B,EAAEO,KAAK,WAIlCY,MAAMS,OACCT,SACRU,KAAK7C,aAAa8C,eAa7BgB,oBAAqB,SAASnD,SAAUoD,WACpClE,EAAE,oBAAsBc,SAAW,MAAME,GAAG,SAAS,SAASC,IAC1DA,GAAGC,qBAECC,EAAInB,EAAEiB,GAAGG,mBAER8C,iBAED/C,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,6CAChCkB,OAAOC,SAASC,KAAO3B,EAAEO,KAAK,aAI9BE,eAAiB3B,IAAI4B,YAAY,CACjC,CACInB,IAAK,SACLC,UAAW,oBAGfmB,aAAe1B,aAAa2B,OAAO,CAACC,KAAM5B,aAAa6B,MAAMC,cAC7DoB,YAAchD,UAAUiD,OAAO,gCAAiC,IAEpEvD,EAAEmC,KAAKP,eAAgBE,cAAcM,MAAK,SAASC,QAASC,cAExDA,MAAMI,UAAU1B,GAAGX,YAAYuD,OAAQtB,MAAMuB,QAAQC,KAAKxB,QAE1DA,MAAMC,SAASF,QAAQ,IACvBC,MAAMG,kBAAkBJ,QAAQ,IAChCC,MAAME,QAAQc,aAEdhB,MAAMI,UAAU1B,GAAGX,YAAYsC,MAAM,WAIb,cAFD3C,EAAE,sCAAsC+D,MAGvD5C,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,uBAGhCP,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,uBAC5B1B,EAAE,sBAAsBmE,GAAG,YAC3BhD,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,qBAEhCP,EAAEO,KAAK,OAAQP,EAAEO,KAAK,QAAU,sBAIxCkB,OAAOC,SAASC,KAAO3B,EAAEO,KAAK,WAIlCY,MAAMS,OACCT,SACRU,KAAK7C,aAAa8C"}