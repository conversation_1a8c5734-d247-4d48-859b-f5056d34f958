{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_analytics/export_options

    Export options.

    The purpose of this template is to render the exporting options.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Example context (json):
    {
    }
}}
<div class="custom-control custom-radio">
    <input class="custom-control-input" type="radio" name="exportoption" id="id-mode-exportdata" value="exportdata">
    <label class="custom-control-label" for="id-mode-exportdata">{{#str}} exporttrainingdata, tool_analytics {{/str}}</label>
</div>
<div class="custom-control custom-radio">
    <input class="custom-control-input" type="radio" name="exportoption" id="id-mode-exportmodel" value="exportmodel" checked>
    <label class="custom-control-label" for="id-mode-exportmodel">{{#str}} exportmodel, tool_analytics {{/str}}</label>
</div>
<div class="custom-control custom-checkbox ml-5" id="id-includeweights-container">
  <input class="custom-control-input" type="checkbox" id="id-includeweights" value="1" checked>
  <label class="custom-control-label" for="id-includeweights">{{#str}} exportincludeweights, tool_analytics {{/str}}</label>
</div>

{{#js}}
    require(['jquery'], function($) {
        $("input[name='exportoption']:radio").change(function() {
            if ($(this).val() == 'exportdata') {
                $('#id-includeweights-container').hide();
            } else {
                $('#id-includeweights-container').show();
            }
        });
    });
{{/js}}
