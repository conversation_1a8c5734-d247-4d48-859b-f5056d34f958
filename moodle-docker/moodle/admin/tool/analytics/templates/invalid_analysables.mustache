{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_analytics/invalid_analysables

    Template for invalid analysables.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * none

    Example context (json):
    {
        "modelname": "Not engaging courses",
        "analysables": [
            {
                "url": "<a href=\"#\">Maths</a>",
                "validtraining": "Ongoing course",
                "validprediction": "Not enough students activity"
            }, {
                "url": "<a href=\"#\">Psichology</a>",
                "validtraining": "No students",
                "validprediction": "No students"
            }
        ]
    }
}}

<div class="box">
    <h3>{{#str}}modelinvalidanalysables, tool_analytics, {{modelname}}{{/str}}</h3>
    <div>{{#str}}invalidanalysablesinfo, tool_analytics{{/str}}</div>
    {{#noanalysables}}
        <div class="mt-2 mb-1">
            {{> core/notification_info}}
        </div>
    {{/noanalysables}}
    {{^noanalysables}}
        <div class="mt-2 mb-1">
            <span>{{#prev}}{{> core/single_button}}{{/prev}}</span>
            <span>{{#next}}{{> core/single_button}}{{/next}}</span>
        </div>
        <table class="generaltable fullwidth">
            <caption class="accesshide">{{#str}}invalidanalysablestable, tool_analytics{{/str}}</caption>
            <thead>
                <tr>
                    <th scope="col">{{#str}}name{{/str}}</th>
                    <th scope="col">{{#str}}invalidtraining, tool_analytics{{/str}}</th>
                    <th scope="col">{{#str}}invalidprediction, tool_analytics{{/str}}</th>
                </tr>
            </thead>
            <tbody>
            {{#analysables}}
                <tr>
                    <td>{{{url}}}</td>
                    <td>{{validtraining}}</td>
                    <td>{{validprediction}}</td>
                </tr>
            {{/analysables}}
            </tbody>
        </table>
        <div class="mt-1 mb-2">
            <span>{{#prev}}{{> core/single_button}}{{/prev}}</span>
            <span>{{#next}}{{> core/single_button}}{{/next}}</span>
        </div>
    {{/noanalysables}}
</div>
