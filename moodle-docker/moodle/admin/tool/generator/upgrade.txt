This files describes API changes in core libraries and APIs,
information provided here is intended especially for developers.

=== 4.2 ===
* Any activity module (mod) that declares the 'xxx_course_backend_generator_create_activity' function in lib.php will now be able to create instance of the
module in the test course. There is now an additional parameter to the admin/tool/generator/cli/maketestcourse.php command so we can whitelist all the modules
that need to be added to the course via the additionalmodules parameter.
See bigbluebuttonbn_course_backend_generator_create_activity as an example.


=== 4.0 ===

* Function tool_generator_testplan_backend::create_users_file() now supports to pass the size of the testing plan,
  that needs to be equal or smaller than the size of the generated site. That's used to effectively restrict the
  exported number of users to the number of threads the jmeter plan will have (previously all the enrolled users
  were being exported, with that leading to "false" loops (users not really looping X times).

=== 3.7 ===

* Function tool_generator_testplan_backend::get_course_options() is removed, the 'course' form element is used instead.
