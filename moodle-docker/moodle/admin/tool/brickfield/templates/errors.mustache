{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_brickfield/errors

    This template renders the list view for the errors accessibility report.

    Example context (json):
    {
        "pagetitle": "Error details",
        "pagenavigation": " ",
        "tableheading1": "Activity",
        "tableheading2": "Check",
        "tableheading3": "Existing HTML code",
        "tableheading4": "Line",
        "tableheading5": "Edit",
        "tabledata": [
            {
                "activity": "Book",
                "check": "Headings",
                "html": "&lt;h3&gt;&lt;/h3&gt;",
                "line": 2,
                "edit": "<a href=\"link.com\">Edit</a>"
            },
            {
                "activity": "Label",
                "check": "Headings",
                "html": "&lt;h3&gt;&lt;/h3&gt;",
                "line": 4,
                "edit": "<a href=\"link.com\">Edit</a>"
            }
        ]
    }
}}

<h3>{{{pagetitle}}}</h3>
{{{pagenavigation}}}
<table class="generaltable">
    <thead>
    <tr>
        <th class="header c0" style="width: 30%;" scope="col">{{tableheading1}}</th>
        <th class="header c1" style="width: 30%;" scope="col">{{tableheading2}}</th>
        <th class="header c2" style="width: 30%;" scope="col">{{tableheading3}}</th>
        <th class="header c3" style="width: 5%;" scope="col">{{tableheading4}}</th>
        <th class="header c4 lastcol" style="width: 5%;" scope="col">{{{tableheading5}}}</th>
    </tr>
    </thead>
    <tbody>
    {{#tabledata}}
    <tr>
        <td class="cell c0" style="width:30%;">{{activity}}</td>
        <td class="cell c1" style="width:30%;">{{check}}</td>
        <td class="cell c2 text-break" style="width:30%;">{{html}}</td>
        <td class="cell c3" style="width:5%;">{{line}}</td>
        <td class="cell c4" style="width:5%;">{{{edit}}}</td>
    </tr>
    {{/tabledata}}
    </tbody>
</table>
