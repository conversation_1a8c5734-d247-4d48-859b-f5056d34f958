"a_links_dont_open_new_window","Links should not open a new window without warning","<p>Links which open a new window using the ""target"" attribute should warn users.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;a href=""webpage.html""&gt;View this page&lt;/a&gt;</code></p><h5>Right</h5><p><code>&lt;a href=""webpage.html""&gt;View this page (opens in new window)&lt;/a&gt;</code></p>","1"
"a_must_contain_text","Links should contain text","<p>Because many users of screen readers use links to navigate the page, providing links with no text (or with images that have empty 'alt' attributes and no other readable text) hinders these users.</p>","1"
"area_dont_open_new_window","No ""area"" elements should open a new window without warning","<p>No <code>area</code> elements should open a new window without warning.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;map name=""anatomymap""&gt;  &lt;area shape=""rect"" coords=""0,0,82,126"" href=""skull.htm"" target=""_blank"" alt=""Human skull""/&gt;  &lt;/map&gt;</code></p><h5>Right</h5><p><code>&lt;map name=""anatomymap""&gt;  &lt;area shape=""rect"" coords=""0,0,82,126"" href=""skull.htm"" target=""_blank"" alt=""Human skull (opens in new window)""/&gt;  &lt;/map&gt;</code></p>","1"
"area_has_alt_value","All ""area"" elements should have an ""alt"" attribute","<p>All <code>area</code> elements within a <code>map</code> should have an ""alt"" attribute.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;map name=""anatomymap""&gt;  &lt;area shape=""rect"" coords=""0,0,82,126"" href=""skull.htm""/&gt;  &lt;/map&gt;</code></p><h5>Right</h5><p><code>&lt;map name=""anatomymap""&gt;  &lt;area shape=""rect"" coords=""0,0,82,126"" href=""skull.htm"" alt=""Human skull""/&gt;  &lt;/map&gt;</code></p>","1"
"a_suspicious_link_text","Link text should be descriptive","<p>Links should be descriptive of the content they're linking to, such as 'Project guidelines' rather than 'guidelines.html' or 'Click here'.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;a href=""webpage.html""&gt;Click here&lt;/a&gt;</code></p><h5>Right</h5><p><code>&lt;a href=""webpage.html""&gt;View Project guidelines here&lt;/a&gt;</code></p>","1"
"basefont_is_not_used","The ""Basefont"" tag should not be used","<p>The <code>basefont</code> tag is deprecated and should not be used. CSS properties should be used instead.</p>","1"
"blink_is_not_used","The ""blink"" tag should not be used","<p>The <code>blink</code> tag should not be used, as it can hinder some users with cognitive disabilities or photosensitive epilepsy or eyesight issues.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;blink&gt;Important text&lt;/blink&gt;</code></p><h5>Right</h5><p><code>&lt;strong&gt;Important text&lt;/strong&gt;</code></p>","1"
"bold_is_not_used","The ""b"" (bold) element should not be used","<p>The <code>b</code> (bold) element provides no emphasis for non-sighted readers. Use the <code>strong</code> tag instead.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;p&gt;This is &lt;b&gt;really important text&lt;/b&gt;.&lt;/p&gt;</code></p><h5>Right</h5><p><code>&lt;p&gt;This is &lt;strong&gt;really important text&lt;/strong&gt;.&lt;/p&gt;</code></p>","1"
"content_too_long","Content length should not exceed 3000 words.","<p>For content longer than 3000 words, consider splitting it up into multiple documents. This makes it easier for students to navigate, process and retain the information, especially those with memory, concentration, reading or mental health issues.</p>","3"
"css_text_has_contrast","Insufficient text color contrast with the background","<p>Text color should be easily viewable and should not be the only indicator of meaning or function. Color contrast should have at least a 4.5:1 ratio for small text and 3:1 ratio for large text.</p>","1"
"embed_has_associated_no_embed","All ""embed"" elements have an associated ""noembed"" element","<p>Because some users cannot use the <code>embed</code> element, provide alternative content in a <code>noembed</code> element.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;embed src=""multimedia.mov""/&gt;</code></p><h5>Right</h5><p><code>&lt;embed src=""multimedia.mov""/&gt;&lt;noembed&gt;Multimedia with &lt;a href=""multimedia.html""&gt;an available transcript&lt;/a&gt;.&lt;/noembed&gt;</code></p>","1"
"header_h3","The header following an h3 should be h1, h2, h3 or h4","<p>Well-structured documents are easier for all users to navigate. The title of a page should have a heading level of H2, each section should have a heading level of H3, each sub-section should be H4, etc.</p>","3"
"headers_have_text","Headings should contain text","<p>Sighted and screen reader users depend on headings to organize and navigate the content on the page. Headings should not be empty and should represent an accurate outline of the relevant content</p>","1"
"i_is_not_used","The ""i"" (italic) element is not used","<p>The <code>i</code> (italic) element provides no emphasis for non-sighted readers. Use the <code>em</code> tag instead.</p><h4>Impact</h4><p>The <code>i</code> (italic) element only conveys appearance, wheras the <code>em</code> element conveys meaning.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;p&gt;This is &lt;i&gt;really important text&lt;/i&gt;.&lt;/p&gt;</code></p><h5>Right</h5><p><code>&lt;p&gt;This is &lt;em&gt;really important text&lt;/em&gt;.&lt;/p&gt;</code></p>","1"
"img_alt_is_different","Alternative Text should not be the image filename","<p>Alternative Text (Alt Text) is an alternative (non-visual) way to describe the meaning of an image. Please provide a brief description of the image for a <a href='http://en.wikipedia.org/wiki/Screen_reader'>screen reader</a> user. Note: It should not be the image file name.</p>","1"
"img_alt_is_too_long","Alternative Text is more than the maximum allowed characters","<p>Alternative Text (Alt Text) is an alternative (non-visual) way to describe the meaning of an image. It should be concise, relevant, meaningful, and no longer than 125 characters ideally.</p>","1"
"img_alt_not_empty_in_anchor","Alt text for all img elements used as source anchors should not be empty","<p>Alternative Text (Alt Text) is an alternative (non-visual) way to describe the destination of an image within an anchor link. Please provide a brief description of the image link destination for a screen reader user.</p>","1"
"img_alt_not_place_holder","Images should not have a simple placeholder text as an ""alt"" attribute","<p>Any image that is not used for decorativon or which is purely for layout purposes cannot have an ""alt"" attribute that consists solely of placeholders. Placeholders include:</p><ul><li>nbsp</li><li>&amp;nbsp;</li><li>spacer</li><li>image</li><li>img</li><li>photo</li></ul><h4>Example</h4><h5>Wrong</h5><p><code>&lt;img src=""dog.jpg"" alt=""image""&gt;</code></p><h5>Right</h5><p><code>&lt;img src=""dog.jpg"" alt=""A photograph of a dog""&gt;</code></p>","1"
"img_has_alt","Image elements should have an ""alt"" attribute","<p>Alternative Text (Alt Text) is an alternative (non-visual) way to describe the meaning of an image. Please provide a brief description of the image for a <a href='http://en.wikipedia.org/wiki/Screen_reader'>screen reader</a> user. Note: It should not be the image file name.</p>","1"
"img_with_map_has_use_map","Any image with an ""ismap"" attribute have a valid ""usemap"" attribute","<p>If an image has an ""ismap"" attribute, it should also have a ""usemap"" attribute.</p>","1"
"legend_text_not_empty","""Legend"" text should not contain just whitespace","<p>If a <code>legend</code> element is used in a fieldset, the <code>legend</code> should not contain empty text.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;fieldset&gt;    &lt;legend&gt; &lt;/legend&gt;    &lt;label for=""name""&gt;Name:&lt;/label&gt; &lt;input type=""text"" size=""30"" id=""name""/&gt;&lt;br /&gt;    &lt;label for=""email""&gt;Email:&lt;/label&gt; &lt;input type=""text"" size=""30"" id=""email""/&gt;&lt;br /&gt;&lt;/fieldset&gt;</code></p><h5>Right</h5><p><code>&lt;fieldset&gt;    &lt;legend&gt;Personal Information:&lt;/legend&gt;    &lt;label for=""name""&gt;Name:&lt;/label&gt; &lt;input type=""text"" size=""30"" id=""name""/&gt;&lt;br /&gt;    &lt;label for=""email""&gt;Email:&lt;/label&gt; &lt;input type=""text"" size=""30"" id=""email""/&gt;&lt;br /&gt;&lt;/fieldset&gt;</code></p>","1"
"marquee_is_not_used","The ""marquee"" tag should not be used","<p>The <code>marquee</code> element is difficult for users to read and is not a standard HTML element. Try to find another way to convey the importance of this text.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;marquee&gt;This is really hard to read&lt;/marquee&gt;</code></p><h5>Right</h5><p><code>&lt;strong&gt;This is much easier to read&lt;/strong&gt;</code></p>","1"
"no_headings","Consider adding headings to your document to create more structure","<p>If appropriate, add headings to the page to organize the content for sighted and <a href='http://en.wikipedia.org/wiki/Screen_reader'>screen reader</a> users. The headings should represent an accurate outline of the content</p>","3"
"object_must_have_embed","Every object should contain an ""embed"" element","<p>Every <code>object</code> element should also contain an <code>embed</code> element.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;object data=""movie.mov""&gt;&lt;/object&gt;</code></p><h5>Right</h5><p><code>&lt;object data=""movie.mov""&gt;&lt;embed src=""movie.mov""&gt;&lt;/embed&gt;&lt;/object&gt;</code></p>","1"
"object_must_have_title","Objects should have a title attribute","<p>All <code>object</code> elements should contain a ""title"" attribute.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;object src=""widget.html""&gt;&lt;/object&gt;</code></p><h5>Right</h5><p><code>&lt;object src=""widget.html"" title=""A small web widget""&gt;&lt;/object&gt;</code></p>","1"
"object_must_have_valid_title","Objects should not have an empty title attribute","<p>All <code>object</code> elements should have a ""title"" attribute which is not empty.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;object src=""widget.html"" title=""""&gt;&lt;/object&gt;</code></p><h5>Right</h5><p><code>&lt;object src=""widget.html"" title=""A small web widget""&gt;&lt;/object&gt;</code></p>","1"
"strike_is_not_used","The ""strike"" (strike-through) element should not be used","<p>The <code>strike</code> element is deprecated and should not be used. It provides no text meaning for non-sighted readers. Use the <code>del</code> (deleted) tag instead.</p><h4>Example</h4><h5>Wrong</h5><p><code>&lt;p&gt;&lt;strike&gt;(10 remaining)&lt;/strike&gt; All sold out!&lt;/p&gt;</code></p><h5>Right</h5><p><code>&lt;p&gt;&lt;del&gt;(10 remaining)&lt;/del&gt; All sold out!&lt;/p&gt;</code></p>","1"
"table_data_should_have_th","No table headers found","<p>Add a table header because it provides a description of the table structure for sighted and <a href='http://en.wikipedia.org/wiki/Screen_reader'>screen reader</a> users.</p>","1"
"table_summary_does_not_duplicate_caption","Table ""summary"" elements should not duplicate the ""caption"" element","<p>The summary and the caption should be different, as both provide different information. A <code>caption</code element identifies the table., while the ""summary"" attribute describes the table contents.</p>","1"
"table_td_should_not_merge","Tables should have no merged cells","<p>Tables which contain merged cells are more problematic for accessibility tools such as screen readers to read correctly.</p>","1"
"table_th_should_have_scope", "No row or column scopes declarations found in headers of the table", "<p>Scope declarations in headers organize and define table data by row/column for sighted and <a href=""http://en.wikipedia.org/wiki/Screen_reader"">screen reader</a> users.</p>", "1"
