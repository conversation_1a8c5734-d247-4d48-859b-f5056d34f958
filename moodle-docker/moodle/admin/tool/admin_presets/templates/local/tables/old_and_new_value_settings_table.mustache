{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_admin_presets/local/tables/old_and_new_value_settings_table

    Template for setting comparison table, showing old and new values.

    Context variables required for this template:
    * beforeapplying - Whether the preset has been already applied or not.

    Example context (json):
    {
        "caption": "Applied settings",
        "settings": [
            {
                "plugin": "quiz",
                "visiblename": "Decimal places in grades",
                "oldvisiblevalue": "0",
                "visiblevalue": "2"
            }
        ]
    }
}}

<table class="generaltable boxaligncenter admin_presets_applied">
    <caption class="accesshide">{{{caption}}}</caption>
    <thead>
        <tr>
            <th scope="col" class="w-50">{{#str}}settingname, tool_admin_presets{{/str}}</th>
            <th scope="col" class="w-25">{{#str}}plugin{{/str}}</th>
            <th scope="col">
                {{^beforeapplying}}
                    {{#str}}oldvalue, tool_admin_presets{{/str}}
                {{/beforeapplying}}
                {{#beforeapplying}}
                    {{#str}}currentvalue, tool_admin_presets{{/str}}
                {{/beforeapplying}}
            </th>
            <th scope="col">{{#str}}newvalue, tool_admin_presets{{/str}}</th>
        </tr>
    </thead>
    <tbody>
        {{#settings}}
            <tr>
                <td>{{{visiblename}}}</td>
                <td>{{{plugin}}}</td>
                <td>{{{oldvisiblevalue}}}</td>
                <td>{{{visiblevalue}}}</td>
            </tr>
        {{/settings}}
    </tbody>
</table>
