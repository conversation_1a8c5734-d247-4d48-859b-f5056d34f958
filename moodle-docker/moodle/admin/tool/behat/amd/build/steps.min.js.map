{"version": 3, "file": "steps.min.js", "sources": ["../src/steps.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport Ajax from 'core/ajax';\nimport Templates from 'core/templates';\nimport PendingJS from 'core/pending';\n\n/**\n * Enhancements for the step definitions page.\n *\n * @module tool_behat/steps\n * @copyright 2022 Catalyst IT EU\n * <AUTHOR> <<EMAIL>>\n * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/**\n * Call the get_entity_generator web service function\n *\n * Takes the name of an entity generator and returns an object containing a list of the required fields.\n *\n * @param {String} entityType\n * @returns {Promise}\n */\nconst getGeneratorEntities = (entityType) => Ajax.call([{\n    methodname: 'tool_behat_get_entity_generator',\n    args: {entitytype: entityType}\n}])[0];\n\n/**\n * Render HTML for required fields\n *\n * Takes the entity data returned from getGeneratorEntities and renders the HTML to display the required fields.\n *\n * @param {String} entityData\n * @return {Promise}\n */\nconst getRequiredFieldsContent = (entityData) => {\n    if (!entityData.required?.length) {\n        return Promise.resolve({\n            html: '',\n            js: ''\n        });\n    }\n    return Templates.renderForPromise('tool_behat/steprequiredfields', {fields: entityData.required});\n};\n\nexport const init = () => {\n    // When an entity is selected in the \"the following exist\" step, fetch and display the required fields.\n    document.addEventListener('change', async(e) => {\n        const entityElement = e.target.closest('.entities');\n        const stepElement = e.target.closest('.stepcontent');\n        if (!entityElement || !stepElement) {\n            return;\n        }\n\n        const pendingPromise = new PendingJS('tool_behat/steps:change');\n\n        const entityData = await getGeneratorEntities(e.target.value);\n        const {html, js} = await getRequiredFieldsContent(entityData);\n\n        const stepRequiredFields = stepElement.querySelector('.steprequiredfields');\n        if (stepRequiredFields) {\n            await Templates.replaceNode(stepRequiredFields, html, js);\n        } else {\n            await Templates.appendNodeContents(stepElement, html, js);\n        }\n        pendingPromise.resolve();\n    });\n};\n"], "names": ["document", "addEventListener", "async", "entityElement", "e", "target", "closest", "stepElement", "pendingPromise", "PendingJS", "entityData", "entityType", "value", "Ajax", "call", "methodname", "args", "entitytype", "html", "js", "required", "_entityData$required", "length", "Templates", "renderForPromise", "fields", "Promise", "resolve", "getRequiredFieldsContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "querySelector", "replaceNode", "appendNodeContents"], "mappings": ";;;;;;;;4NA2DoB,KAEhBA,SAASC,iBAAiB,UAAUC,MAAAA,UAC1BC,cAAgBC,EAAEC,OAAOC,QAAQ,aACjCC,YAAcH,EAAEC,OAAOC,QAAQ,oBAChCH,gBAAkBI,yBAIjBC,eAAiB,IAAIC,iBAAU,2BAE/BC,iBAlCgBC,WAkCwBP,EAAEC,OAAOO,MAlClBC,cAAKC,KAAK,CAAC,CACpDC,WAAY,kCACZC,KAAM,CAACC,WAAYN,eACnB,IAH0BA,IAAAA,iBAmChBO,KAACA,KAADC,GAAOA,SAtBaT,CAAAA,0EACzBA,WAAWU,0CAAXC,qBAAqBC,OAMnBC,mBAAUC,iBAAiB,gCAAiC,CAACC,OAAQf,WAAWU,WAL5EM,QAAQC,QAAQ,CACnBT,KAAM,GACNC,GAAI,MAkBiBS,CAAyBlB,YAE5CmB,mBAAqBtB,YAAYuB,cAAc,uBACjDD,yBACMN,mBAAUQ,YAAYF,mBAAoBX,KAAMC,UAEhDI,mBAAUS,mBAAmBzB,YAAaW,KAAMC,IAE1DX,eAAemB"}