This files describes API changes in the tool_behat code.

=== 4.2.1 ===
* Behat is initialised with Axe accessibility tests enabled by default, if you want to disable them please use the --no-axe option.
=== 3.7 ===
* Behat will now look for behat step definitions in the current
  theme and any parents the theme may have.
=== 2.7 ===
* Constants behat_base::cap_allow, behat_base::cap_prevent and
  behat_base::cap_prohibit have been removed in favour of the
  lang/en/role.php language strings 'allow', 'prevent' and 'prohibit'.
* @_only_local tag used in .feature files replaced by @_file_upload tag
* @_alerts tag used in .feature files replaced by @_alert tag
