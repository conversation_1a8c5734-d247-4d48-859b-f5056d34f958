.path-admin-tool-capability [data-search="capability"] {
    min-width: 675px;
}

.path-admin-tool-capability .comparisontable {
    margin-top: 150px;
}

.path-admin-tool-capability .comparisontable th,
.path-admin-tool-capability .comparisontable td {
    vertical-align: middle;
    padding: 0.4em 0.5em 0.3em;
}

.path-admin-tool-capability .comparisontable thead th {
    vertical-align: bottom;
    background: none;
}

.path-admin-tool-capability .comparisontable thead th div {
    position: relative;
}

.path-admin-tool-capability .comparisontable thead th div > a {
    position: absolute;
    top: -1.75em;
    left: 1em;
    width: 150px;
    text-align: left;
    margin-bottom: 1em;
    text-indent: -1.45em;
    -webkit-transform-origin: top left;
    -moz-transform-origin: top left;
    -ms-transform-origin: top left;
    -o-transform-origin: top left;
    -webkit-transform: rotate(315deg);
    -moz-transform: rotate(315deg);
    -ms-transform: rotate(315deg);
    -o-transform: rotate(315deg);
}

.path-admin-tool-capability .comparisontable tbody th {
    background-color: #eee;
    text-align: right;
    border: 1px solid #dfdfdf;
}

.path-admin-tool-capability .comparisontable tbody th span {
    display: block;
    color: #666;
    font-size: 80%;
}

.path-admin-tool-capability .comparisontable tbody td {
    border: 1px solid #dfdfdf;
}

.path-admin-tool-capability .comparisontable .inherit {
    color: #666;
}

.path-admin-tool-capability .comparisontable .allow {
    background-color: #060;
    font-weight: bold;
    color: white;
}

.path-admin-tool-capability .comparisontable .prevent {
    background-color: #ad6704;
    font-weight: bold;
    color: white;
}

.path-admin-tool-capability .comparisontable .prohibit {
    background-color: #800;
    font-weight: bold;
    color: white;
}