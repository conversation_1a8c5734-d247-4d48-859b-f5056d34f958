{"version": 3, "file": "search.min.js", "sources": ["../src/search.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Add search filtering of capabilities\n *\n * @module      tool_capability/search\n * @copyright   2023 Paul <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Pending from 'core/pending';\nimport {debounce} from 'core/utils';\n\nconst Selectors = {\n    capabilityOverviewForm: '#capability-overview-form',\n    capabilitySelect: '[data-search=\"capability\"]',\n    capabilitySearch: '[data-action=\"search\"]',\n};\n\nconst debounceTimer = 250;\n\n/**\n * Initialize module\n */\nexport const init = () => {\n    const capabilityOverviewForm = document.querySelector(Selectors.capabilityOverviewForm);\n    if (!capabilityOverviewForm) {\n        return;\n    }\n\n    const capabilitySelect = capabilityOverviewForm.querySelector(Selectors.capabilitySelect);\n    const capabilitySearch = capabilityOverviewForm.querySelector(Selectors.capabilitySearch);\n\n    const capabilitySelectFilter = searchTerm => {\n        const pendingPromise = new Pending('tool_capability/search:filter');\n\n        // Remove existing options, remembering which were previously selected.\n        let capabilitySelected = [];\n        capabilitySelect.querySelectorAll('option').forEach(option => {\n            if (option.selected) {\n                capabilitySelected.push(option.value);\n            }\n            option.remove();\n        });\n\n        // Filter for matching capabilities.\n        const availableCapabilities = JSON.parse(capabilitySelect.dataset.availableCapabilities);\n        const filteredCapabilities = Object.keys(availableCapabilities).reduce((matches, capability) => {\n            if (availableCapabilities[capability].toLowerCase().includes(searchTerm)) {\n                matches[capability] = availableCapabilities[capability];\n            }\n            return matches;\n        }, []);\n\n        // Re-create filtered options.\n        Object.entries(filteredCapabilities).forEach(([capability, capabilityText]) => {\n            const option = document.createElement('option');\n            option.value = capability;\n            option.innerText = capabilityText;\n            option.selected = capabilitySelected.indexOf(capability) > -1;\n            capabilitySelect.append(option);\n        });\n\n        pendingPromise.resolve();\n    };\n\n    // Cache initial capability options.\n    const availableCapabilities = {};\n    capabilitySelect.querySelectorAll('option').forEach(option => {\n        availableCapabilities[option.value] = option.text;\n    });\n    capabilitySelect.dataset.availableCapabilities = JSON.stringify(availableCapabilities);\n\n    // Debounce the event listener on the search element to allow user to finish typing.\n    const capabilitySearchDebounce = debounce(capabilitySelectFilter, debounceTimer);\n    capabilitySearch.addEventListener('keyup', event => {\n        const pendingPromise = new Pending('tool_capability/search:keyup');\n\n        capabilitySearchDebounce(event.target.value.toLowerCase());\n        setTimeout(() => {\n            pendingPromise.resolve();\n        }, debounceTimer);\n    });\n\n    // Ensure filter is applied on form load.\n    if (capabilitySearch.value !== '') {\n        capabilitySelectFilter(capabilitySearch.value.toLowerCase());\n    }\n};\n"], "names": ["Selectors", "capabilityOverviewForm", "document", "querySelector", "capabilitySelect", "capabilitySearch", "capabilitySelectFilter", "searchTerm", "pendingPromise", "Pending", "capabilitySelected", "querySelectorAll", "for<PERSON>ach", "option", "selected", "push", "value", "remove", "availableCapabilities", "JSON", "parse", "dataset", "filteredCapabilities", "Object", "keys", "reduce", "matches", "capability", "toLowerCase", "includes", "entries", "_ref", "capabilityText", "createElement", "innerText", "indexOf", "append", "resolve", "text", "stringify", "capabilitySearchDebounce", "addEventListener", "event", "target", "setTimeout"], "mappings": ";;;;;;;kJA0BMA,iCACsB,4BADtBA,2BAEgB,6BAFhBA,2BAGgB,uCAQF,WACVC,uBAAyBC,SAASC,cAAcH,sCACjDC,oCAICG,iBAAmBH,uBAAuBE,cAAcH,4BACxDK,iBAAmBJ,uBAAuBE,cAAcH,4BAExDM,uBAAyBC,mBACrBC,eAAiB,IAAIC,iBAAQ,qCAG/BC,mBAAqB,GACzBN,iBAAiBO,iBAAiB,UAAUC,SAAQC,SAC5CA,OAAOC,UACPJ,mBAAmBK,KAAKF,OAAOG,OAEnCH,OAAOI,kBAILC,sBAAwBC,KAAKC,MAAMhB,iBAAiBiB,QAAQH,uBAC5DI,qBAAuBC,OAAOC,KAAKN,uBAAuBO,QAAO,CAACC,QAASC,cACzET,sBAAsBS,YAAYC,cAAcC,SAAStB,cACzDmB,QAAQC,YAAcT,sBAAsBS,aAEzCD,UACR,IAGHH,OAAOO,QAAQR,sBAAsBV,SAAQmB,WAAEJ,WAAYK,2BACjDnB,OAASX,SAAS+B,cAAc,UACtCpB,OAAOG,MAAQW,WACfd,OAAOqB,UAAYF,eACnBnB,OAAOC,SAAWJ,mBAAmByB,QAAQR,aAAe,EAC5DvB,iBAAiBgC,OAAOvB,WAG5BL,eAAe6B,WAIbnB,sBAAwB,GAC9Bd,iBAAiBO,iBAAiB,UAAUC,SAAQC,SAChDK,sBAAsBL,OAAOG,OAASH,OAAOyB,QAEjDlC,iBAAiBiB,QAAQH,sBAAwBC,KAAKoB,UAAUrB,6BAG1DsB,0BAA2B,mBAASlC,uBAvDxB,KAwDlBD,iBAAiBoC,iBAAiB,SAASC,cACjClC,eAAiB,IAAIC,iBAAQ,gCAEnC+B,yBAAyBE,MAAMC,OAAO3B,MAAMY,eAC5CgB,YAAW,KACPpC,eAAe6B,YA7DL,QAkEa,KAA3BhC,iBAAiBW,OACjBV,uBAAuBD,iBAAiBW,MAAMY"}