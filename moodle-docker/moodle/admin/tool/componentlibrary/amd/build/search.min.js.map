{"version": 3, "file": "search.min.js", "sources": ["../src/search.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Interface to the Lunr search engines.\n *\n * @module     tool_componentlibrary/search\n * @copyright  2021 Bas Brands <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport lunrJs from 'tool_componentlibrary/lunr';\nimport selectors from 'tool_componentlibrary/selectors';\nimport Log from 'core/log';\nimport Notification from 'core/notification';\nimport {enter, escape} from 'core/key_codes';\n\nlet lunrIndex = null;\nlet pagesIndex = null;\n\n/**\n * Get the jsonFile that is generated when the component library is build.\n *\n * @method\n * @private\n * @param {String} jsonFile the URL to the json file.\n * @return {Object}\n */\nconst fetchJson = async(jsonFile) => {\n    const response = await fetch(jsonFile);\n\n    if (!response.ok) {\n        Log.debug(`Error getting Hugo index file: ${response.status}`);\n    }\n\n    return await response.json();\n};\n\n/**\n * Initiate lunr on the data in the jsonFile and add the jsondata to the pagesIndex\n *\n * @method\n * @private\n * @param {String} jsonFile the URL to the json file.\n */\nconst initLunr = jsonFile => {\n    fetchJson(jsonFile).then(jsondata => {\n        pagesIndex = jsondata;\n        // Using an arrow function here will break lunr on compile.\n        lunrIndex = lunrJs(function() {\n            this.ref('uri');\n            this.field('title', {boost: 10});\n            this.field('content');\n            this.field('tags', {boost: 5});\n            jsondata.forEach(p => {\n                this.add(p);\n            });\n        });\n        return null;\n    }).catch(Notification.exception);\n};\n\n/**\n * Setup the eventlistener to listen on user input on the search field.\n *\n * @method\n * @private\n */\nconst initUI = () => {\n    const searchInput = document.querySelector(selectors.searchinput);\n    searchInput.addEventListener('keyup', e => {\n        const query = e.currentTarget.value;\n        if (query.length < 2) {\n            document.querySelector(selectors.dropdownmenu).classList.remove('show');\n            return;\n        }\n        renderResults(searchIndex(query));\n    });\n    searchInput.addEventListener('keydown', e => {\n        if (e.keyCode === enter) {\n            e.preventDefault();\n        }\n        if (e.keyCode === escape) {\n            searchInput.value = '';\n        }\n    });\n};\n\n/**\n * Trigger a search in lunr and transform the result.\n *\n * @method\n * @private\n * @param  {String} query\n * @return {Array} results\n */\nconst searchIndex = query => {\n    // Find the item in our index corresponding to the lunr one to have more info\n    // Lunr result:\n    //  {ref: \"/section/page1\", score: 0.2725657778206127}\n    // Our result:\n    //  {title:\"Page1\", href:\"/section/page1\", ...}\n\n    return lunrIndex.search(query + ' ' + query + '*').map(result => {\n        return pagesIndex.filter(page => {\n            return page.uri === result.ref;\n        })[0];\n    });\n};\n\n/**\n * Display the 10 first results\n *\n * @method\n * @private\n * @param {Array} results to display\n */\nconst renderResults = results => {\n    const dropdownMenu = document.querySelector(selectors.dropdownmenu);\n    if (!results.length) {\n        dropdownMenu.classList.remove('show');\n        return;\n    }\n\n    // Clear out the results.\n    dropdownMenu.innerHTML = '';\n\n    const baseUrl = M.cfg.wwwroot + '/admin/tool/componentlibrary/docspage.php';\n\n    // Only show the ten first results\n    results.slice(0, 10).forEach(function(result) {\n        const link = document.createElement(\"a\");\n        const chapter = result.uri.split('/')[1];\n        link.appendChild(document.createTextNode(`${chapter} > ${result.title}`));\n        link.classList.add('dropdown-item');\n        link.href = baseUrl + result.uri;\n\n        dropdownMenu.appendChild(link);\n    });\n\n    dropdownMenu.classList.add('show');\n};\n\n/**\n * Initialize module.\n *\n * @method\n * @param {String} jsonFile Full path to the search DB json file.\n */\nexport const search = jsonFile => {\n    initLunr(jsonFile);\n    initUI();\n};\n"], "names": ["lunrIndex", "pagesIndex", "initLunr", "jsonFile", "async", "response", "fetch", "ok", "debug", "status", "json", "<PERSON><PERSON><PERSON>", "then", "jsondata", "ref", "field", "boost", "for<PERSON>ach", "p", "add", "catch", "Notification", "exception", "searchIndex", "query", "search", "map", "result", "filter", "page", "uri", "renderResults", "results", "dropdownMenu", "document", "querySelector", "selectors", "dropdownmenu", "length", "classList", "remove", "innerHTML", "baseUrl", "M", "cfg", "wwwroot", "slice", "link", "createElement", "chapter", "split", "append<PERSON><PERSON><PERSON>", "createTextNode", "title", "href", "searchInput", "searchinput", "addEventListener", "e", "currentTarget", "value", "keyCode", "enter", "preventDefault", "escape", "initUI"], "mappings": ";;;;;;;gQA6BIA,UAAY,KACZC,WAAa,WA2BXC,SAAWC,WAjBCC,OAAAA,iBACRC,eAAiBC,MAAMH,iBAExBE,SAASE,iBACNC,+CAAwCH,SAASI,eAG5CJ,SAASK,QAWtBC,CAAUR,UAAUS,MAAKC,WACrBZ,WAAaY,SAEbb,WAAY,kBAAO,gBACVc,IAAI,YACJC,MAAM,QAAS,CAACC,MAAO,UACvBD,MAAM,gBACNA,MAAM,OAAQ,CAACC,MAAO,IAC3BH,SAASI,SAAQC,SACRC,IAAID,SAGV,QACRE,MAAMC,sBAAaC,YAqCpBC,YAAcC,OAOTxB,UAAUyB,OAAOD,MAAQ,IAAMA,MAAQ,KAAKE,KAAIC,QAC5C1B,WAAW2B,QAAOC,MACdA,KAAKC,MAAQH,OAAOb,MAC5B,KAWLiB,cAAgBC,gBACZC,aAAeC,SAASC,cAAcC,mBAAUC,kBACjDL,QAAQM,mBACTL,aAAaM,UAAUC,OAAO,QAKlCP,aAAaQ,UAAY,SAEnBC,QAAUC,EAAEC,IAAIC,QAAU,4CAGhCb,QAAQc,MAAM,EAAG,IAAI7B,SAAQ,SAASU,cAC5BoB,KAAOb,SAASc,cAAc,KAC9BC,QAAUtB,OAAOG,IAAIoB,MAAM,KAAK,GACtCH,KAAKI,YAAYjB,SAASkB,yBAAkBH,sBAAatB,OAAO0B,SAChEN,KAAKR,UAAUpB,IAAI,iBACnB4B,KAAKO,KAAOZ,QAAUf,OAAOG,IAE7BG,aAAakB,YAAYJ,SAG7Bd,aAAaM,UAAUpB,IAAI,yBASThB,WAClBD,SAASC,UAlFE,YACLoD,YAAcrB,SAASC,cAAcC,mBAAUoB,aACrDD,YAAYE,iBAAiB,SAASC,UAC5BlC,MAAQkC,EAAEC,cAAcC,MAC1BpC,MAAMc,OAAS,EACfJ,SAASC,cAAcC,mBAAUC,cAAcE,UAAUC,OAAO,QAGpET,cAAcR,YAAYC,WAE9B+B,YAAYE,iBAAiB,WAAWC,IAChCA,EAAEG,UAAYC,kBACdJ,EAAEK,iBAEFL,EAAEG,UAAYG,oBACdT,YAAYK,MAAQ,QAoE5BK"}