{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more comments.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/data_requests

    Data requests page.

    Classes required for JS:
    * requestactions

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * newdatarequesturl string The URL pointing to the data request creation page.
    * datarequests string The HTML of the data requests table.

    Example context (json):
    {
        "newdatarequesturl": "#",
        "datarequests": "<table><tr><td>This is the table where the list of data requests will be rendered</td></tr></table>",
        "filter": {
            "action": "#",
            "filteroptions": [
                {
                    "value": "1",
                    "label": "Option 1"
                },
                {
                    "value": "2",
                    "label": "Option 2",
                    "selected": true
                },
                {
                    "value": "3",
                    "label": "Option 3",
                    "selected": true
                }
            ]
        }
    }
}}

{{#httpsite}}
    {{> core/notification_warning}}
{{/httpsite}}

<div data-region="datarequests">
    <div class="mt-1 mb-1">
        <div class="float-right">
            <a href="{{newdatarequesturl}}" class="btn btn-primary" data-action="new-request">
                {{#str}}newrequest, tool_dataprivacy{{/str}}
            </a>
        </div>
        {{#filter}}
            {{>tool_dataprivacy/request_filter}}
        {{/filter}}
    </div>

    <div class="position-relative mt-1 mb-1" data-region="data-requests-table">
        {{{datarequests}}}
    </div>
</div>

{{#js}}
// Initialise the JS.
require(['tool_dataprivacy/requestactions'], function(ActionsMod) {
    new ActionsMod();
});
{{/js}}
