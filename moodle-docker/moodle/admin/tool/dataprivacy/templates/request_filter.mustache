{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/request_filter

    Template for the request filter element.

    Context variables required for this template:
    * action string - The action URL for the form.
    * filteroptions - Array of filter options.
      * value string - The option value.
      * label string - The option label.
      * selected boolean - Whether the option is selected

    Example context (json):
    {
        "action": "#",
        "filteroptions": [
            {
                "value": "1",
                "label": "Option 1"
            },
            {
                "value": "2",
                "label": "Option 2",
                "selected": true
            },
            {
                "value": "3",
                "label": "Option 3",
                "selected": true
            },
            {
                "value": "4",
                "label": "Option 4"
            }
        ]
    }
}}
<form method="post" action="{{action}}" class="mb-1" role="search" id="request_filter_form">
    <label for="request-filters" class="sr-only">{{#str}}filters{{/str}}</label>
    <select name="request-filters[]" id="request-filters" multiple="multiple" class="form-autocomplete-original-select">
        {{#filteroptions}}
            <option value="{{value}}" {{#selected}}selected="selected"{{/selected}}>{{{label}}}</option>
        {{/filteroptions}}
    </select>
    <input type="hidden" id="filters-cleared" name="filters-cleared" value="0" />
</form>
{{#js}}
require(['tool_dataprivacy/request_filter'], function(Filter) {
    Filter.init();
});
{{/js}}
