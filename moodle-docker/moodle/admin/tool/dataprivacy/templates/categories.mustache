{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/categories

    Manage categories.

    Classes required for JS:

    Data attributes required for JS:

    Context variables required for this template:
    * categories - array of objects
    * actions - array of actions (already in HTML).

    Example context (json):
    {
        "categoriesexist": 1,
        "categories": [
            {
                "name" : "Category 1",
                "description": "<strong>Description 1</strong>",
                "actions": [
                ]
            }, {
                "name" : "Category 2",
                "description": "<strong>Description 2</strong>",
                "actions": [
                ]
            }
        ]
    }
}}

{{#navigation}}
    {{> core/action_link}}
{{/navigation}}

<div data-region="categories" class="mt-3 mb-1">
    <div class="my-1">
        <button class="btn btn-secondary" data-add-element="category" title="{{#str}}addcategory, tool_dataprivacy{{/str}}">
            {{#pix}}t/add, moodle, {{#str}}addcategory, tool_dataprivacy{{/str}}{{/pix}}
        </button>
    </div>
    <table class="table table-striped table-hover">
        <caption class="accesshide">{{#str}}categorieslist, tool_dataprivacy{{/str}}</caption>
        <thead>
            <tr>
                <th scope="col">{{#str}}name{{/str}}</th>
                <th scope="col" class="w-50">{{#str}}description{{/str}}</th>
                <th scope="col">{{#str}}actions{{/str}}</th>
            </tr>
        </thead>
        <tbody>
            {{#categories}}
            <tr data-categoryid="{{id}}">
                <td>{{{name}}}</td>
                <td>{{{description}}}</td>
                <td>
                    {{#actions}}
                        {{> core/action_menu}}
                    {{/actions}}
                </td>
            </tr>
            {{/categories}}
        </tbody>
    </table>
    {{^categories}}
        <p>
            {{#str}}nocategories, tool_dataprivacy{{/str}}
        </p>
    {{/categories}}
</div>
