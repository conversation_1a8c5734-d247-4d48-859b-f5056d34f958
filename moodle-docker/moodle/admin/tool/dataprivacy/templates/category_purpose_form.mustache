{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/category_purpose_form

    Manage data registry defaults.

    Classes required for JS:

    Data attributes required for JS:

    Context variables required for this template:
    * actionurl String The action URL.
    * contextlevel Number - The context level.
    * modemodule <PERSON> - Whether to display the activity select element.
    * activityoptions Array - An array of objects for the activity select element.
    * categoryoptions Array - An array of objects for the category select element.
    * purposeoptions Array - An array of objects for the purpose select element.

    Example context (json):
    {
        "actionurl": "#",
        "contextlevel": 70,
        "newactivitydefaults": true,
        "modemodule": true,
        "activityoptions": [
            { "name": "assign", "displayname": "Assignment" },
            { "name": "forum", "displayname": "Forum", "selected": true },
            { "name": "lesson", "displayname": "Lesson" },
            { "name": "quiz", "displayname": "Quiz" }
        ],
        "categoryoptions": [
            { "id": 1, "name": "Category 1" },
            { "id": 2, "name": "Category 2", "selected": true },
            { "id": 3, "name": "Category 3" }
        ],
        "purposeoptions": [
            { "id": 1, "name": "Purpose 1" },
            { "id": 2, "name": "Purpose 2" },
            { "id": 3, "name": "Purpose 3", "selected": true }
        ]
    }
}}

<div class="alert alert-warning" role="alert">
    {{#str}}defaultswarninginfo, tool_dataprivacy{{/str}}
</div>
<form method="post" action="{{actionurl}}" id="category_purpose_form">
    <input type="hidden" value="{{contextlevel}}" id="contextlevel" />
    {{#modemodule}}
    <div class="form-group">
        <label for="activity">{{#str}}activitymodule{{/str}}</label>
        {{^newactivitydefaults}}
            <input type="hidden" id="activity" value="{{#activityoptions}}{{#selected}}{{name}}{{/selected}}{{/activityoptions}}" />
        {{/newactivitydefaults}}
        <select class="form-control" {{#newactivitydefaults}}id="activity" {{/newactivitydefaults}}{{^newactivitydefaults}}disabled{{/newactivitydefaults}}>
            {{#activityoptions}}
                <option value="{{name}}" {{#selected}}selected{{/selected}}>{{displayname}}</option>
            {{/activityoptions}}
        </select>
    </div>
    {{/modemodule}}
    <div class="form-group">
        <label for="category">{{#str}}category, tool_dataprivacy{{/str}}</label>
        <select class="form-control" id="category">
        {{#categoryoptions}}
            <option value="{{id}}" {{#selected}}selected{{/selected}}>{{name}}</option>
        {{/categoryoptions}}
        </select>
    </div>
    <div class="form-group">
        <label for="purpose">{{#str}}purpose, tool_dataprivacy{{/str}}</label>
        <select class="form-control" id="purpose">
        {{#purposeoptions}}
            <option value="{{id}}" {{#selected}}selected{{/selected}}>{{name}}</option>
        {{/purposeoptions}}
        </select>
    </div>
    <div class="form-check">
        <input class="form-check-input" type="checkbox" value="1" id="override">
        <label class="form-check-label" for="override">
            {{#str}}overrideinstances, tool_dataprivacy{{/str}}
        </label>
    </div>
</form>
