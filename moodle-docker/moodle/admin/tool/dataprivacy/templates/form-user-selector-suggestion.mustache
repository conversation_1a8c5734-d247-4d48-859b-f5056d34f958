{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/form-user-selector-suggestion

    Moodle template for the list of valid options in an autocomplate form element.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * fullname string Users full name
    * email string user email field

    Example context (json):
    {
        "fullname": "Admin User",
        "extrafields": [
            {
                "name": "email",
                "value": "<EMAIL>"
            },
            {
                "name": "phone1",
                "value": "**********"
            }
        ]
    }
}}
<span>
    <span>{{fullname}}</span>
    {{#extrafields}}
        <span><small>{{{value}}}</small></span>
    {{/extrafields}}
</span>
