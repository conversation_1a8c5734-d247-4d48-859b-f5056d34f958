{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/context_tree_branches

    Context tree.

    Classes required for JS:

    Data attributes required for JS:

    Context variables required for this template:

    Example context (json):
    {
    }

}}

{{#branches}}
    <nav class="nav-pills flex-column">
        {{> tool_dataprivacy/context_tree_node}}
    </nav>
{{/branches}}
