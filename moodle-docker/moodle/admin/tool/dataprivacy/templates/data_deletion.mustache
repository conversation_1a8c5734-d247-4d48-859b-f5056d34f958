{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more comments.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/data_deletion

    Data deletion page.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * filter - The context data for single_select element that has the options for the table filter.
    * expiredcontexts - The HTML for the table of expired contexts.

    Example context (json):
    {
        "filter": {
            "name": "filter",
            "method": "get",
            "action": "#",
            "classes": "singleselect",
            "label": "",
            "disabled": false,
            "title": null,
            "formid": "single_select_f5ac5e42bb035319",
            "id": "single_select5ac5e42bb035320",
            "params":[],
            "options":[
                {"value": 50, "name": "Course", "selected": true, "optgroup": false},
                {"value": 70, "name": "Activities and resources", "selected":false, "optgroup": false},
                {"value": 80, "name": "Blocks", "selected": false, "optgroup": false}
            ],
            "labelattributes": [],
            "helpicon": false
        },
        "expiredcontexts": "<table class='table'><tbody><tr><td>This is the table that will contain the list of expired contexts</td></tr></tbody></table>"
    }
}}
<div class="container-fluid" data-region="data-deletion">
    <div class="row" data-region="top-nav">
        <div class="alert alert-info">
            {{#str}}datadeletionpagehelp, tool_dataprivacy{{/str}}
        </div>
        <div class="float-left">
            {{#filter}}
                {{> core/single_select}}
            {{/filter}}
        </div>
        {{#existingcontexts}}
            <div class="float-right">
                <button data-action="markfordeletion" class="btn btn-secondary">{{#str}}deleteselected, moodle{{/str}}</button>
            </div>
        {{/existingcontexts}}
    </div>
    <div class="row mt-1 mb-1" data-region="expired-contexts-table">
        {{{expiredcontexts}}}
    </div>
    <div class="row" data-region="bottom-nav">
        {{#existingcontexts}}
            <div class="float-right">
                <button data-action="markfordeletion" class="btn btn-secondary">{{#str}}deleteselected, moodle{{/str}}</button>
            </div>
        {{/existingcontexts}}
    </div>
</div>
{{#js}}
// Initialise the JS.
require(['tool_dataprivacy/data_deletion'], function(DataDeletion) {
    new DataDeletion();
});
{{/js}}
