{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/context_tree_node

    A tree branch.

    Classes required for JS:

    Data attributes required for JS:

    Context variables required for this template:

    Example context (json):
    {
    }

}}


<a class="nav-link {{#active}}active{{/active}}" href="#" data-context-tree-node="1"
        {{#contextlevel}} data-contextlevel="{{.}}" {{/contextlevel}}
        {{#contextid}} data-contextid="{{.}}" {{/contextid}}
        {{#expandcontextid}} data-expandcontextid="{{.}}" {{/expandcontextid}}
        {{#expandelement}} data-expandelement="{{.}}" {{/expandelement}}
        data-expanded="{{expanded}}">
    <i class="fa
        {{#expandelement}}{{#expanded}}fa-minus{{/expanded}}{{/expandelement}}
        {{#expandelement}}{{^expanded}}fa-plus{{/expanded}}{{/expandelement}}
        {{^expandelement}}{{#expanded}}fa-folder-open{{/expanded}}{{/expandelement}}
        {{^expandelement}}{{^expanded}}fa-file{{/expanded}}{{/expandelement}}"
        >
    </i>
    {{text}}
</a>
{{> tool_dataprivacy/context_tree_branches}}
