{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_dataprivacy/data_requests_bulk_actions

    Moodle template for the bulk action select element in the data requests page.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * options - Array of options for the select with value and name.
    * perpage - HTML content of the records per page select element.

    Example context (json):
    {
        "options": [
            {
                "value": 1,
                "name": "Approve"
            },
            {
                "value": 2,
                "name": "Deny"
            }
        ],
        "perpage" : "<div class='singleselect'></div>"
    }
}}
<div class="mt-1 d-inline-block w-100">
    <div class="float-left">
        <select id="bulk-action" class="select custom-select">
        {{#options}}
            <option value="{{ value }}">{{ name }}</option>
        {{/options}}
        </select>
        <button class="btn btn-primary" id="confirm-bulk-action">{{# str}} confirm {{/ str}}</button>
    </div>
    <div class="float-right">
        {{{ perpage }}}
    </div>
</div>
