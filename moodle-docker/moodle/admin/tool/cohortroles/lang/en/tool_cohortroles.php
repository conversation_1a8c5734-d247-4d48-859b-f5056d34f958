<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'tool_userroles', language 'en'
 *
 * @package    tool_cohortroles
 * @copyright  2015 Damyon Wiese
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['acohortroleassignmentssaved'] = '{$a} cohort role assignments were saved.';
$string['assign'] = 'Assign';
$string['assignroletocohort'] = 'Assign user-context roles to all cohort members';
$string['backgroundsync'] = 'Note: New cohort role assignments will not take effect immediately. Role assignment changes will be made by a scheduled task.';
$string['cohortroleassignmentremoved'] = 'The cohort role assignment was removed.';
$string['cohortroleassignmentnotremoved'] = 'The cohort role assignment was not removed.';
$string['cohortroles'] = 'Cohort roles';
$string['existingcohortroles'] = 'Existing cohort role assignments';
$string['managecohortroles'] = 'Assign user roles to cohort';
$string['noassignableroles'] = 'There are currently no roles that can be assigned in the user context. <a href="../../roles/manage.php">Manage roles</a>';
$string['nocohortroleassignmentssaved'] = 'No cohort role assignments were saved.';
$string['onecohortroleassignmentsaved'] = 'One cohort role assignment was saved.';
$string['pluginname'] = 'Cohort roles management';
$string['removecohortroleassignment'] = 'Remove cohort role assignment';
$string['removecohortroleassignmentconfirm'] = 'Are you sure you want to remove this cohort role assignment? This role will be removed for this user in all other user contexts.';
$string['selectcohorts'] = 'Select cohorts';
$string['selectrole'] = 'Select role';
$string['selectusers'] = 'Select users to assign role';
$string['taskname'] = 'Sync cohort role assignments';
$string['thisuserroles'] = 'Roles assigned relative to this user';
$string['privacy:metadata:tool_cohortroles'] = 'The Cohort roles management plugin stores user cohort role mappings.';
$string['privacy:metadata:tool_cohortroles:id'] = 'The ID of the cohort role mapping record';
$string['privacy:metadata:tool_cohortroles:cohortid'] = 'The ID of the cohort';
$string['privacy:metadata:tool_cohortroles:roleid'] = 'The ID of the role';
$string['privacy:metadata:tool_cohortroles:userid'] = 'The ID of the user';
$string['privacy:metadata:tool_cohortroles:timecreated'] = 'The time when the cohort role mapping was created';
$string['privacy:metadata:tool_cohortroles:timemodified'] = 'The time when the cohort role mapping was modified';
$string['privacy:metadata:tool_cohortroles:usermodified'] = 'The ID of the user who last modified the cohort role mapping';
