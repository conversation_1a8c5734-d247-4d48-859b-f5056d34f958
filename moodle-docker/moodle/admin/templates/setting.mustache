{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting

    Admin setting template.

    Context variables required for this template:
    * labelfor - id of the form element
    * title - Setting title
    * override - Overridden message
    * warning - Warning message
    * name - Setting name
    * error - Error message
    * element - The Element HTML
    * forceltr - Force this element to be displayed LTR
    * default - Default value
    * dependenton - optional message listing the settings this one is dependent on

    Example context (json):
    {
        "title": "Setting title",
        "labelfor": "id0",
        "override": "Overidden",
        "warning": "Warning",
        "name": "Name",
        "error": "Error",
        "element": "Raw HTML",
        "forceltr": false,
        "default": "Default value"
    }
}}
{{!
    Setting.
}}
<div class="form-item row" id="{{id}}">
    <div class="form-label col-sm-3 text-sm-right">
        {{#customcontrol}}
            <p {{#labelfor}}id="{{labelfor}}_label"{{/labelfor}}>
                {{{title}}}
                {{#override}}
                    <div class="alert alert-info">{{override}}</div>
                {{/override}}
                {{#warning}}
                    <div class="alert alert-warning">{{warning}}</div>
                {{/warning}}
            </p>
        {{/customcontrol}}
        {{^customcontrol}}
            <label {{#labelfor}}for="{{labelfor}}"{{/labelfor}}>
                {{{title}}}
                {{#override}}
                    <div class="alert alert-info">{{override}}</div>
                {{/override}}
                {{#warning}}
                    <div class="alert alert-warning">{{warning}}</div>
                {{/warning}}
            </label>
        {{/customcontrol}}
        <span class="form-shortname d-block small text-muted">{{{name}}}</span>
    </div>
    <div class="form-setting col-sm-9">
        {{#error}}
            <div><span class="error">{{error}}</span></div>
        {{/error}}
        {{{element}}}
        {{#default}}
            <div class="form-defaultinfo text-muted {{#forceltr}}text-ltr{{/forceltr}}">{{{default}}}</div>
        {{/default}}
        <div class="form-description mt-3">{{{description}}}</div>
        {{#dependenton}}<div class="form-dependenton mb-4 text-muted">{{{.}}}</div>{{/dependenton}}
    </div>
</div>
{{#customcontrol}}
    {{#js}}
        require(['jquery'], function($) {
            $('#{{id}}_label').css('cursor', 'default');
            $('#{{id}}_label').click(function() {
                $('#{{id}}')
                    .find('button, a, input:not([type="hidden"]), select, textarea, [tabindex]')
                    .filter(':not([disabled]):not([tabindex="0"]):not([tabindex="-1"])')
                    .first().focus();
            });
        });
    {{/js}}
{{/customcontrol}}
