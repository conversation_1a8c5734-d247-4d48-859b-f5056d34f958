{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/settings_search_results

    Admin setting search results template.

    Context variables required for this template:
    * actionurl - Url to post to
    * hasresults - True if there are results
    * results - List of results containing url, title, path (array of strings), settings (array of raw html)
    * showsave - Show save buttons

    Example context (json):
    {
        "actionurl": "/",
        "hasresults": true,
        "results": [
            { "url": "/", "title": "Match!", "path": ["Administration", "Match!"], "settings": [ "blah blah blah" ] }
        ]
    }
}}
<form action="{{actionurl}}" method="post" id="adminsettings">
    <div>
        <input type="hidden" name="sesskey" value="{{sesskey}}">
        <input type="hidden" name="action" value="save-settings">
    </div>
    <fieldset>
        <div class="clearer"></div>
        <h2 class="main">{{#str}}searchresults, admin{{/str}}</h2>
        {{#hasresults}}
            {{#results}}
                <h3 class="adminpagetitle"><a href="{{url}}">{{{title}}}</a></h3>
                <ul class="adminpagepath" aria-label="{{#str}} pagepath, core {{/str}}">
                    {{#path}}
                    <li class="small text-muted">{{.}}</li>
                    {{/path}}
                </ul>
                <fieldset class="adminsettings">
                    {{#settings}}
                        <div class="clearer"></div>
                        {{{.}}}
                    {{/settings}}
                </fieldset>
            {{/results}}
            {{#showsave}}
                <div class="row">
                    <div class="offset-sm-3 col-sm-3">
                        <button type="submit" class="btn btn-primary">{{#str}}savechanges, admin{{/str}}</button>
                    </div>
                </div>
            {{/showsave}}
        {{/hasresults}}
        {{^hasresults}}
            {{#str}}noresults, admin{{/str}}
        {{/hasresults}}
    </fieldset>
</form>
