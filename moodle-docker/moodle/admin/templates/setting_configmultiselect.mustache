{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configmultiselect

    Admin multiselect setting template.

    Context variables required for this template:
    * name - form element name
    * id - element id
    * size - element size
    * options - list of options containing name, value, selected
    * readonly - bool

    Example context (json):
    {
        "name": "test",
        "id": "test0",
        "readonly": false,
        "size": "3",
        "options": [ { "name": "Option 1", "value": "V", "selected": true },
                     { "name": "Option 2", "value": "V", "selected": true } ]
    }
}}
{{!
    Setting configmultiselect.
}}
<div class="form-select">
    <input type="hidden" name="{{name}}[xxxxx]" value="1">
    <select {{#readonly}}disabled{{/readonly}} id="{{id}}" name="{{name}}[]" size="{{size}}" class="form-control" multiple>
        {{#options}}
            <option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
        {{/options}}
    </select>
</div>

