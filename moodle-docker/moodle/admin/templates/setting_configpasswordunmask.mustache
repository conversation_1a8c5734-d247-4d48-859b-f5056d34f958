{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configpasswordunmask

    Admin password unmask setting template.

    Context variables required for this template:
    * name - form element name
    * size - form element size
    * value - form element value
    * id - element id
    * readonly - has value been defined in config.php

    Example context (json):
    {
        "name": "test",
        "id": "test0",
        "size": "8",
        "value": "secret",
        "readonly": false
    }
}}
{{#readonly}}
    <div class="form-password">
        <input type="text"
               name = "{{ name }}"
               id="{{ id }}"
               value="********"
               size="{{ size }}"
               class="form-control"
               disabled
        >
    </div>
{{/readonly}}
{{^readonly}}
<div class="form-password">
    <span data-passwordunmask="wrapper" data-passwordunmaskid="{{ id }}">
        <span data-passwordunmask="editor">
            <input  type="password"
                    autocomplete="new-password"
                    name="{{ name }}"
                    id="{{ id }}"
                    value="{{ value }}"
                    data-size="{{ size }}"
                    class="form-control d-none"
                    >
        </span>
        <a href="#" data-passwordunmask="edit" title="{{ edithint }}">
            <span data-passwordunmask="displayvalue">{{> core_form/element-passwordunmask-fill }}</span>
            {{# pix }} t/passwordunmask-edit, core, {{# str }} passwordunmaskedithint, form {{/ str }}{{/ pix }}
        </a>
        <a href="#" data-passwordunmask="unmask" title="{{ unmaskhint }}">
            {{# pix }} t/passwordunmask-reveal, core, {{# str }} passwordunmaskrevealhint, form {{/ str }}{{/ pix }}
        </a>
        <span data-passwordunmask="instructions" class="form-text text-muted" style="display: none;">
            {{# str }} passwordunmaskinstructions, form {{/ str }}
        </span>
    </span>
</div>
{{#js}}
require(['core_form/passwordunmask'], function(PasswordUnmask) {
    new PasswordUnmask("{{ id }}");
});
{{/js}}
{{/readonly}}
